# Node & package manager stuff
node_modules/
.pnpm/
dist/
build/
.out/
.next/
.vercel/
.vscode/

# Output & cache folders
.cache/
turbo/
coverage/
public/build/
public/dist/

# Generated files & types
**/*.d.ts
**/*.d.ts.map

# Environment files
.env*
!.env.example

# System files
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Git ignored files (optional but useful if you want to match .gitignore)
.git/

# lockfiles
pnpm-lock.yaml

# IDE specific junk (optional)
.idea/
*.iml

# Storybook outputs
storybook-static/

# Other tool specific outputs
*.tgz
*.zip

# Optional: ignore static exports
out/

# Optional: Ignore mock API servers / stubs
mocks/
