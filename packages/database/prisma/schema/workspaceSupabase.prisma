model WorkspaceSupabaseConnection {
  id             String    @id @default(uuid())
  workspace      Workspace @relation(fields: [workspaceId], references: [id])
  workspaceId    String    @unique
  accessToken    String
  refreshToken   String
  project        Json?
  organizationId String?   @map("organization_id")
  expiresAt      DateTime
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  @@map("workspace_supabase_connections")
}
