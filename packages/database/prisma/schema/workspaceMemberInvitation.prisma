model WorkspaceMemberInvitation {
  id            String        @id @default(uuid())
  workspace     Workspace     @relation(fields: [workspace_id], references: [id])
  role          WorkspaceRole @relation(fields: [role_id], references: [id])
  code          String        @unique @default(uuid())
  invited_by    User          @relation("workspace_members_invitations_by", fields: [invited_by_id], references: [id])
  created_at    DateTime      @default(now())
  updated_at    DateTime      @updatedAt
  deleted_at    DateTime?
  workspace_id  String
  role_id       Int
  invited_by_id Int

  @@map("workspace_member_invitations")
}
