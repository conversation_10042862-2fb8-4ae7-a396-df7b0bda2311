model ProjectModule {
  id            String   @id @default(uuid())
  projectPlanId String
  moduleId      String // Reference to module in blueprint
  data          Json // Detailed module implementation
  status        String   @default("planned") // "planned", "detailed", "implemented"
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  projectPlan ProjectPlan   @relation(fields: [projectPlanId], references: [id], onDelete: Cascade)
  pages       ProjectPage[]

  @@unique([projectPlanId, moduleId])
}
