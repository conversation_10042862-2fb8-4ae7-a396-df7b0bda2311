model WorkspaceMember {
  id                String        @id @default(uuid())
  user              User          @relation(fields: [user_id], references: [id])
  workspace         Workspace     @relation(fields: [workspace_id], references: [id])
  role              WorkspaceRole @relation(fields: [workspace_role_id], references: [id])
  created_at        DateTime      @default(now())
  updated_at        DateTime      @updatedAt
  deleted_at        DateTime?
  user_id           Int
  workspace_id      String
  workspace_role_id Int

  @@map("workspace_members")
}
