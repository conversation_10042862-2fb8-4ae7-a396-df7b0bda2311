model WorkspaceVercelConnection {
  id           String    @id @default(uuid())
  workspace    Workspace @relation(fields: [workspaceId], references: [id])
  workspaceId  String    @unique
  accessToken  String
  refreshToken String?
  project      Json?
  organization Json?
  expiresAt    DateTime
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  @@map("workspace_vercel_connections")
}
