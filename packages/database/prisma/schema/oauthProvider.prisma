model OauthProvider {
  id         Int       @id @default(autoincrement())
  name       String
  identifier String    @unique
  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)
  user_id    Int
  expires_at DateTime?
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime?

  @@index([identifier], map: "oauth_providers_identifier_idx")
  @@map("oauth_providers")
}
