-- AlterTable
ALTER TABLE `workspaces` ADD COLUMN `github` <PERSON><PERSON><PERSON> NULL,
    ADD COLUMN `metadata` JSON NULL;

-- CreateTable
CREATE TABLE `OnboardingProgress` (
    `id` VARCHAR(191) NOT NULL,
    `userId` INTEGER NOT NULL,
    `data` JSON NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `OnboardingProgress_userId_key`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `OnboardingProgress` ADD CONSTRAINT `OnboardingProgress_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
