/*
  Warnings:

  - You are about to drop the column `anon<PERSON>ey` on the `workspace_supabase_connections` table. All the data in the column will be lost.
  - You are about to drop the column `apiUrl` on the `workspace_supabase_connections` table. All the data in the column will be lost.
  - You are about to drop the column `organizationId` on the `workspace_supabase_connections` table. All the data in the column will be lost.
  - You are about to drop the column `projectName` on the `workspace_supabase_connections` table. All the data in the column will be lost.
  - You are about to drop the column `projectRef` on the `workspace_supabase_connections` table. All the data in the column will be lost.
  - You are about to drop the column `serviceRoleKey` on the `workspace_supabase_connections` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `workspace_supabase_connections` DROP COLUMN `anonKey`,
    DROP COLUMN `apiUrl`,
    DROP COLUMN `organizationId`,
    DROP COLUMN `projectName`,
    DROP COLUMN `projectRef`,
    DROP COLUMN `serviceRoleKey`,
    ADD COLUMN `organization_id` VARCHAR(191) NULL,
    ADD COLUMN `project` JSON NULL;

-- CreateTable
CREATE TABLE `ProjectModule` (
    `id` VARCHAR(191) NOT NULL,
    `projectPlanId` VARCHAR(191) NOT NULL,
    `moduleId` VARCHAR(191) NOT NULL,
    `data` JSON NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'planned',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ProjectModule_projectPlanId_moduleId_key`(`projectPlanId`, `moduleId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ProjectModule` ADD CONSTRAINT `ProjectModule_projectPlanId_fkey` FOREIGN KEY (`projectPlanId`) REFERENCES `ProjectPlan`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
