-- CreateTable
CREATE TABLE `PageChatHistory` (
    `id` VARCHAR(191) NOT NULL,
    `projectPageId` VARCHAR(191) NOT NULL,
    `userId` INTEGER NULL,
    `message` TEXT NOT NULL,
    `role` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ProjectPage` (
    `id` VARCHAR(191) NOT NULL,
    `projectModuleId` VARCHAR(191) NOT NULL,
    `pageId` VARCHAR(191) NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `pageType` VARCHAR(191) NOT NULL,
    `layoutId` VARCHAR(191) NOT NULL,
    `route` VARCHAR(191) NOT NULL,
    `isClientComponent` BOOLEAN NULL DEFAULT false,
    `additionalData` JSON NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ProjectPage_projectModuleId_pageId_key`(`projectModuleId`, `pageId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `workspace_vercel_connections` (
    `id` VARCHAR(191) NOT NULL,
    `workspaceId` VARCHAR(191) NOT NULL,
    `accessToken` VARCHAR(191) NOT NULL,
    `refreshToken` VARCHAR(191) NOT NULL,
    `project` JSON NULL,
    `organization` JSON NULL,
    `expiresAt` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `workspace_vercel_connections_workspaceId_key`(`workspaceId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `PageChatHistory` ADD CONSTRAINT `PageChatHistory_projectPageId_fkey` FOREIGN KEY (`projectPageId`) REFERENCES `ProjectPage`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PageChatHistory` ADD CONSTRAINT `PageChatHistory_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ProjectPage` ADD CONSTRAINT `ProjectPage_projectModuleId_fkey` FOREIGN KEY (`projectModuleId`) REFERENCES `ProjectModule`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `workspace_vercel_connections` ADD CONSTRAINT `workspace_vercel_connections_workspaceId_fkey` FOREIGN KEY (`workspaceId`) REFERENCES `workspaces`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
