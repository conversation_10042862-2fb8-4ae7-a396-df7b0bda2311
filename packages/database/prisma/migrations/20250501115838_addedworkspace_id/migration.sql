/*
  Warnings:

  - You are about to drop the column `userId` on the `OnboardingProgress` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[workspaceId]` on the table `OnboardingProgress` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `workspaceId` to the `OnboardingProgress` table without a default value. This is not possible if the table is not empty.

*/
-- DropF<PERSON><PERSON><PERSON>ey
ALTER TABLE `OnboardingProgress` DROP FOREIGN KEY `OnboardingProgress_userId_fkey`;

-- DropIndex
DROP INDEX `OnboardingProgress_userId_key` ON `OnboardingProgress`;

-- AlterTable
ALTER TABLE `OnboardingProgress` DROP COLUMN `userId`,
    ADD COLUMN `workspaceId` VARCHAR(191) NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX `OnboardingProgress_workspaceId_key` ON `OnboardingProgress`(`workspaceId`);

-- AddForeignKey
ALTER TABLE `OnboardingProgress` ADD CONSTRAINT `OnboardingProgress_workspaceId_fkey` <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (`workspaceId`) REFERENCES `workspaces`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
