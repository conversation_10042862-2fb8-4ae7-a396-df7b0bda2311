{"name": "@iammoderator/db", "version": "0.0.0", "exports": {".": "./src/index.ts"}, "scripts": {"db:generate": "prisma generate", "db:push": "prisma db push --skip-generate", "db:validate": "prisma validate", "db:migrate": "prisma migrate dev", "db:seed": "prisma db seed"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@prisma/client": "^6.0.1"}, "devDependencies": {"prisma": "^6.0.1"}, "prisma": {"seed": "ts-node ./prisma/seed"}}