{"name": "@repo/ui", "version": "0.0.0", "type": "module", "private": true, "exports": {"./button": "./src/button.tsx", "./card": "./src/card.tsx", "./code": "./src/code.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^1.12.4", "@types/node": "^20.11.24", "@types/react": "18.3.0", "@types/react-dom": "18.3.1", "typescript": "5.5.4"}, "dependencies": {"react": "19.0.0-rc-5c56b873-20241107", "react-dom": "19.0.0-rc-5c56b873-20241107"}}