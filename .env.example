################ Frontend Env #######################
VITE_API_URL = "http://localhost:9700/api"
# must be the same port as the worker env SERVER_PORT
VITE_SERVER_PORT = 3001  
################ Frontend Env #######################


################ Backend Env #######################
COREAPI_PORT = 9700
DATABASE_URL="mysql://root:password@localhost:3306/iammodDB"

JWT_ACCESS_SECRET="SOhdskjwdwdowdj"
JWT_REFRESH_SECRET="LSihdiohdkjdhjhwudhj"
JWT_2FA_ACCESS_SECRET="dspsdlmsdmsld"


GOOGLE_CLIENT_ID="697608092651-uocd4isasbndvt676u1fphsv9m0oe9sh.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI="http://localhost:9700/api/auth/google/callback"

#Ensures your tokens are signed with a key that only your server knows.
OAUTH_GOOGLE_STATE= "SuSunajkdjkdjmnbmdbjd"

GITHUB_TOKEN=""
BASE_REPO_OWNER=deva-raja
BASE_REPO_NAME=base-template

# ai tokens
OPENROUTER_API_KEY=
REQUESTY_API_KEY=


# supabase tokens
SUPA_CONNECT_CLIENT_ID=
SUPA_CONNECT_CLIENT_SECRET=
SUPA_CONNECT_REDIRECT_URI=


# Vercel OAuth
VERCEL_CLIENT_ID=
VERCEL_CLIENT_SECRET=
# The callback URL must match what you configured in your Vercel integration settings
VERCEL_REDIRECT_URI=
################ Backend Env ###################


################ Worker Env #######################
SERVER_PORT=3001
WORKER_PORT=3002
REDIS_HOST=localhost
REDIS_PORT=6379
BUILD_TIMEOUT=300000  # 5 minutes
MAX_MEMORY=512  # MB
MAX_CPU=0.5  # CPU cores
KOYEB_API_KEY=
PREVIEW_TIMEOUT=300000  # 5 minutes
TEMP_DIRECTORY_PATH=/tmp/koyeb-previews
################ Worker Env #######################