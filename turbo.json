{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^db:generate", "^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"], "env": ["VITE_API_URL", "VITE_SERVER_PORT"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"dependsOn": ["^db:generate"], "cache": false, "persistent": true}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:validate": {"cache": false}, "start:server": {"cache": false, "persistent": true}, "start:worker": {"cache": false, "persistent": true}}}