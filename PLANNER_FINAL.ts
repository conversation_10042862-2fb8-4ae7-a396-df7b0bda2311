/**
 * Core entity type with essential metadata.
 * This base interface provides common fields for all entities in the system.
 */
interface Entity {
  id: string; // Unique identifier for the entity
  title: string; // Human-readable name
  status: 'planned' | 'in_progress' | 'completed' | 'deprecated'; // Current development status
  tags?: string[]; // Optional metadata for searching and categorization
}

/**
 * Dependency tracking for establishing relationships between tasks.
 * Keeps separate arrays for different types of dependencies to maintain clarity.
 */
interface Dependency {
  prerequisites?: string[]; // Conceptual dependency ids (must understand first)
  buildDependencies?: string[]; // Technical dependency ids (must build first)
}

/**
 * Represents a code snippet with metadata.
 * Used for both complete implementations and partial examples.
 */
interface CodeSnippet {
  title: string; // Name of the code snippet
  language:
    | 'ts'
    | 'js'
    | 'jsx'
    | 'tsx'
    | 'py'
    | 'html'
    | 'css'
    | 'json'
    | 'graphql'
    | 'sql'
    | 'sh'; // Language of the snippet
  code: string; // The actual code content
  context: 'frontend' | 'backend' | 'testing' | 'deployment'; // Where this code is meant to be used
  description?: string; // Optional explanation of code's purpose
  scope: 'global' | 'module' | 'page' | 'component'; // Scope of the code's applicability

  // Next.js specific fields
  filePath?: string; // Path where this file should be placed in Next.js app structure
  isClientComponent: boolean; // Whether this is a Next.js Client Component (defaults to true)
  isServerComponent?: boolean; // Whether this is a Next.js Server Component (defaults to false)
}

/**
 * Defines the structure of TanStack Query configurations
 */
interface TanStackQueryConfig {
  queryKey: string[]; // The query key array
  queryFn: string; // Function to fetch data (code snippet or reference)
  options?: {
    // Optional settings
    staleTime?: number; // Time in ms before query is considered stale
    cacheTime?: number; // Time in ms to keep unused data in cache
    refetchOnWindowFocus?: boolean; // Whether to refetch when window regains focus
    refetchOnMount?: boolean; // Whether to refetch when component mounts
    retry?: number | boolean; // Retry behavior
    enabled?: boolean; // Whether the query should run automatically
    initialData?: any; // Initial data for the query
  };
  isInfiniteQuery?: boolean; // Whether this is an infinite query
  mutations?: {
    // Associated mutations
    name: string; // Name of the mutation
    mutationFn: string; // Function to perform mutation
  }[];
}

/**
 * Defines an API endpoint specification.
 * Provides complete information needed to implement or consume an API.
 */
interface APIEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'; // HTTP method
  path: string; // URL path for the endpoint
  requestSchema?: object; // Schema for the request payload
  responseSchema?: object; // Schema for the expected response
  requiresAuth: boolean; // Whether authentication is required
  description?: string; // Purpose of this endpoint
  scope: 'global' | 'module' | 'page'; // Scope of the API's usage

  // Implementation approach
  implementation:
    | 'supabase'
    | 'nextjs-api'
    | 'nextjs-server-action'
    | 'external'; // How this API is implemented

  // Supabase specific fields
  supabaseConfig?: {
    table?: string; // Supabase table name
    operation: 'select' | 'insert' | 'update' | 'delete' | 'rpc'; // Supabase operation
    queryParams?: object; // Additional query parameters
    rpcFunction?: string; // Name of RPC function if operation is "rpc"
  };

  // TanStack Query configuration if applicable
  tanstackQuery?: TanStackQueryConfig;

  // Next.js API route specific fields (for cases where Supabase isn't sufficient)
  nextjsConfig?: {
    routePath: string; // File path for the API route
    middleware?: string[]; // Middleware to apply to this route
    isClientComponent: boolean; // Whether API call happens in client component (default true)
  };
}

/**
 * Defines a Supabase table or view configuration.
 */
interface SupabaseTable {
  name: string; // Table name
  schema: 'public' | 'auth' | 'storage' | string; // Schema name
  columns: {
    // Column definitions
    name: string; // Column name
    type: string; // Column type (e.g., "text", "uuid", "integer")
    nullable: boolean; // Whether the column can be null
    isPrimaryKey: boolean; // Whether this is a primary key
    hasDefault: boolean; // Whether the column has a default value
    foreignKey?: {
      // Foreign key reference if applicable
      table: string; // Referenced table
      column: string; // Referenced column
    };
  }[];
  policies?: {
    // RLS policies
    name: string; // Policy name
    operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'ALL'; // Operation this policy applies to
    definition: string; // Policy definition (using SQL-like syntax)
  }[];
}

/**
 * Implementation details for a story.
 * Contains all technical information needed to build the feature.
 */
interface Implementation {
  description: string; // High-level implementation approach
  code?: CodeSnippet[]; // Code snippets for the implementation
  apis?: APIEndpoint[]; // API endpoints used or created

  // Data models section optimized for Supabase
  dataModels?: {
    name: string; // Name of the model
    schema: object; // Schema definition
    description?: string; // Purpose of this data model
    scope: 'global' | 'module' | 'page'; // Visibility/accessibility of this model
    supabaseTable?: SupabaseTable; // Supabase table configuration if applicable
  }[];

  // Simplified state management approach (defaults to Zustand)
  stateManagement?: {
    approach: 'zustand' | 'useState'; // Default to Zustand
    keyStates: string[]; // Key state variables used
    scope: 'global' | 'module' | 'page' | 'component'; // Scope of the state
    implementation?: string; // Implementation code for the Zustand store
  };

  // TanStack Query configurations (with preference for this approach)
  queries?: TanStackQueryConfig[];

  // Shadcn components used
  shadcnComponents?: {
    name: string; // Component name (e.g., "Button", "Card")
    props?: object; // Typical props for this usage
    customization?: string; // Any tailwind customizations
  }[];

  // Client-side form handling with react-hook-form
  forms?: {
    name: string; // Form name
    fields: {
      // Form fields
      name: string; // Field name
      type: string; // Field type
      validation?: object; // Validation rules
      defaultValue?: any; // Default value
      shadcnComponent?: string; // Shadcn component to use
    }[];
    onSubmit: string; // Submit handler code or reference
  }[];

  // Table configurations
  tables?: {
    name: string; // Table name
    columns: {
      // Column definitions
      id: string; // Column identifier
      header: string; // Column header text
      accessorKey?: string; // Data access path
      cell?: string; // Custom cell renderer
    }[];
    features: string[]; // Enabled features
    dataSource: string; // Data source reference
  }[];
}

/**
 * Enumeration of different story types to establish hierarchy and priority.
 */
type StoryType =
  | 'epic' // High-level feature grouping
  | 'feature' // Substantial user-facing functionality
  | 'component' // Reusable UI element
  | 'task' // Regular implementation task
  | 'sub_task'; // Breakdown of a larger task (lower priority)

/**
 * Stories are the atomic building blocks of features.
 * Each story represents a specific piece of functionality to be implemented.
 */
interface Story extends Entity, Dependency {
  type: StoryType; // Type of story affecting priority and scope
  parentId?: string; // Optional reference to parent story
  complexity: number; // 1-5 scale indicating implementation difficulty
  breakdownIds?: string[]; // References to sub-tasks that break down this story

  design?: {
    // Design specifications
    components?: string[]; // UI components to be used
  };

  implementation: Implementation; // Technical implementation details

  // Explicitly define component scope and reusability
  componentScope?: 'global' | 'module' | 'page'; // Where this component can be used

  // Next.js file path
  filePath?: string; // Where this component or page should be placed in the file structure
  isClientComponent: boolean; // Whether this is a Client Component (default to true)
}

/**
 * Represents a Next.js layout that can be shared across multiple pages.
 */
interface NextjsLayout extends Entity {
  type: 'auth' | 'dashboard' | 'public' | 'admin' | 'custom'; // Layout category
  implementation: Implementation; // Implementation details
  filePath: string; // Path in app directory structure (e.g., "app/(dashboard)/layout.tsx")
  isRootLayout: boolean; // Whether this is the root layout
  metadata?: {
    // Next.js metadata for the layout
    title?: string; // Page title template
    description?: string; // Page description template
    additionalMetadata?: object; // Any additional metadata
  };
  supportedSegments: string[]; // URL segments this layout supports

  // Shadcn specific
  shadcnProviders?: {
    // Providers needed for this layout
    name: string; // Provider name
    config?: object; // Provider configuration
  }[];

  // Default to client component for layouts
  isClientComponent: boolean; // Default to true
}

/**
 * Page types to categorize different UI screens.
 */
type PageType =
  | 'auth' // Authentication-related pages
  | 'dashboard' // Data visualization pages
  | 'form' // Data input pages
  | 'list' // Data listing pages
  | 'detail' // Single item detail pages
  | 'settings' // Configuration pages
  | 'landing' // Marketing/entry pages
  | 'error'; // Error pages

/**
 * Pages group related stories that form a cohesive UI screen.
 * Each page typically maps to a distinct URL route in the application.
 */
interface Page extends Entity, Dependency {
  type: PageType; // Page category
  layoutId: string; // Reference to the layout this page uses
  stories: Story[]; // Collection of stories comprising this page
  route: string; // URL path for this page
  description: string; // Detailed explanation of page purpose and functionality

  // Next.js specific fields
  filePath: string; // Path in app directory (e.g., "app/dashboard/page.tsx")
  isClientComponent: boolean; // Default to true for client component
  pageParams?: {
    // Parameters for dynamic routes
    name: string; // Parameter name
    type: string; // Parameter type
    isOptional: boolean; // Whether parameter is optional
  }[];
}

/**
 * Defines a Zustand store configuration.
 */
interface ZustandStore extends Entity {
  scope: 'global' | 'module' | 'page'; // Visibility level of this state
  schema: object; // Schema of the state
  initialState: object; // Initial state values

  // Zustand-specific
  selectors?: {
    // Selectors to derive state
    name: string; // Name of selector
    implementation: string; // Selector implementation
  }[];

  actions: {
    // Store actions
    name: string; // Action name
    implementation: string; // Action implementation code
  }[];

  // Persistence options
  persistence?: {
    type: 'localStorage' | 'sessionStorage' | 'none'; // Persistence method
    key?: string; // Storage key
  };

  // Next.js specific implementation
  filePath: string; // Where the store file should be located

  // Auth state specific fields
  isAuthStore?: boolean; // Whether this manages auth state
}

/**
 * Represents a shared UI component that can be reused across pages.
 */
interface SharedComponent extends Entity {
  scope: 'global' | 'module'; // Where this component can be used
  implementation: Implementation; // Component implementation

  // Shadcn component info
  shadcn?: {
    isCustomized: boolean; // Whether this customizes a Shadcn component
    baseName?: string; // The base Shadcn component name if customized
    requiredComponents?: string[]; // Additional Shadcn components needed
  };

  // Next.js specific
  filePath: string; // Where this component should be placed
  isClientComponent: boolean; // Default to true for client component

  props: {
    // Component props/inputs
    name: string; // Name of the prop
    type: string; // Type of the prop
    required: boolean; // Whether the prop is required
    defaultValue?: any; // Default value if not required
  }[];
}

/**
 * Configuration for TanStack Query at module level.
 */
interface QueryConfig extends Entity {
  scope: 'global' | 'module'; // Scope of these queries
  filePath: string; // File path for query definitions

  // Query definitions
  queries: TanStackQueryConfig[];

  // Prefetching configuration
  prefetchingStrategy?: {
    type: 'static' | 'dynamic'; // Static (at build time) or dynamic prefetching
    implementation?: string; // Implementation details
  };

  // Axios configuration for this module/globally
  axiosConfig?: {
    baseURL?: string; // Base URL for API calls
    timeout?: number; // Default timeout
    headers?: object; // Default headers
    interceptors?: {
      // Axios interceptors
      request?: string; // Request interceptor code
      response?: string; // Response interceptor code
    };
  };
}

/**
 * Modules group related pages into functional areas.
 * Represents a major section of the application with cohesive functionality.
 */
interface Module extends Entity, Dependency {
  pages: Page[]; // Collection of pages in this module
  description: string; // Purpose and scope of this module
  layouts?: NextjsLayout[]; // Layouts specific to this module
  zustandStores?: ZustandStore[]; // Zustand stores for this module
  sharedComponents?: SharedComponent[]; // Components shared across pages in this module
  // Next.js specific
  routeSegment?: string; // URL segment for this module
}

/**
 * Product configuration defines the entire application.
 * Contains global settings and references to all modules.
 */
interface Product extends Entity {
  modules: Module[]; // All modules in the product
  description: string; // Overview of the product purpose and capabilities
  globalLayouts: NextjsLayout[]; // Layouts shared across all modules
  globalZustandStores: ZustandStore[]; // Global Zustand stores
  globalComponents: SharedComponent[]; // Application-wide components
  globalQueryConfigs: QueryConfig[]; // Global query configurations
}
