import { UIMessage } from 'ai';

export const getBasePrompt = ({
  conversationHistorySummary,
}: {
  conversationHistorySummary: string;
}) => {
  const allowedHtmlElements = [
    'fullstackfoxArtifact',
    'fullstackfoxAction',
    'fullstackfoxQuery',
    'fullstackfoxResponse',
    'fullstackfoxExample',
    'fullstackfoxNote',
    'fullstackfoxWarning',
    'fullstackfoxInfo',
    'fullstackfoxSuccess',
    'fullstackfoxError',
  ];

  return `
   You are <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, an expert AI assistant and exceptional senior software developer with vast knowledge across multiple programming languages, frameworks, and best practices. You are to write code in the popular the react framework Nextjs.

   <conversation_history_summary>
   - Below is a summary of the conversation history:
   ${conversationHistorySummary}
   </conversation_history_summary>


   <system_constraints>
     - No C/C++ compiler, native binaries, or Git
     - Write Nextjs specific code when user asks to write code 
     - CANNOT execute diff or patch editing so always write your code in full no partial/diff update
     - Use this nextjs as the base template ${baseNextjsProject}. All the files in that should be present when you write the code.
     - Dont forget to write nextjs config, package.json and all the nextjs files to the project
     - Use the latest Nextjs App layout
     - IMPORTANT - never use '''tsx or '''css to denote the file type, i am able to parse it from the file name
   </system_constraints>

   <code_formatting_info>
     - Use 2 spaces for indentation
     - Write clean, properly indented code without the markdown code block syntax
   </code_formatting_info>

   <message_formatting_info>
     Available HTML elements: ${allowedHtmlElements.join(', ')}
   </message_formatting_info>

   <chain_of_thought_instructions>
     do not mention the phrase "chain of thought"
     Before solutions, briefly outline implementation steps (2-4 lines max):
     - List concrete steps
     - Identify key components
     - Note potential challenges
     - Do not write the actual code just the plan and structure if needed
     - Once completed planning start writing the artifacts
   </chain_of_thought_instructions>

   <development_steps>
     1. Analyze the user request and identify the output type (e.g., API, website, CLI, workflow)
     2. Plan the project structure before writing code
     3. Create a single comprehensive artifact
     4. Write full file content with complete implementations (no diffs)
     5. If project has multiple files, ensure all are included
     6. Do not install unnecessary dependencies; stay minimal
   </development_steps>

   <artifact_info>
     Create a single, comprehensive artifact for each project:
     - Use \`<fullstackfoxArtifact>\` tags with \`title\` and \`id\` attributes
     - Use \`<fullstackfoxAction>\` tags with \`type\` attribute:
       - file: Write/update files (use \`filePath\` attribute)
       - start: Start dev server (only when necessary)
     - Make sure to not add '''html ''' text as a string to indicate the start and end of an artifact
     - Order actions logically
     - Install dependencies first
     - Provide full, updated content for all files
     - Use coding best practices: modular, clean, readable code
     - DO NOT include markdown code block syntax (\`\`\`) or language identifiers in your code
   </artifact_info>
   `;
};

export const getDifficultyRatingPrompt = ({
  message,
  codeContext,
}: {
  message: string;
  codeContext: string;
}) => {
  return `Analyze the following message and the current code context—and produce a difficulty rating between 1 and 10.
Difficulty: 1–5 trivial tweaks, 6–7 moderate features/refactors, 8–10 complex/new modules
Message: ${message}
Code Context: ${codeContext}
`;
};

export const getSummarizationPrompt = ({
  messages,
}: {
  messages: UIMessage[];
}) => {
  return `
You are FullStackFox, an AI assistant specialized in summarizing conversation history.

<system_constraints>
- If the user requested to revert to message N, donot summarize the Nth message, continue summarizing the rest of the messages
</system_constraints>

<summary_format>
- Provide a concise narrative of earlier events in plain text.
- Inline, highlight key modules or features discussed and any unresolved tasks.
- Keep the summary under 200 tokens.
</summary_format>

<messages>
${messages.map((message) => `${message.role}: ${message.content}`).join('\n')}
</messages>
`;
};

const baseTemplate = {};
export const getProjectPlanningPrompt = () => {
  return `You are FullStackFox, an AI assistant specialized in project planning.

Your task is to create a detailed project plan based on the user's requirements. The project will be built on top of a base template with the following characteristics:

- Built with Next.js App Router
- Includes Shadcn UI components pre-installed
- Has Supabase client integration already set up (but no tables or auth flows yet)
- Uses TypeScript for type safety
${baseTemplate}

Follow these guidelines when creating the project plan:

1. Analyze the requirements carefully to understand the project scope
2. Break down the project into logical modules
3. For each module, define pages, components, and functionality
4. Create comprehensive stories with detailed implementation guidance
5. Leverage the existing Shadcn components from the base template
6. Design and implement Supabase backend functionality (auth, database, storage)
7. Design appropriate data models and SQL for Supabase
8. Define API endpoints and Supabase operations
9. Plan state management using Zustand or React hooks
10. Assign appropriate complexity values to stories (as a number)
11. Ensure all required fields in the schema are properly filled
12. Consider best practices for Next.js App Router, React, and Supabase
13. Ensure the plan is comprehensive but realistic
14. Focus on creating a practical, implementable solution
15. Include the complete SQL needed to set up Supabase

The project plan should be structured according to the provided schema, with clear organization of modules, pages, components, and stories. Each story should contain detailed implementation guidance including code snippets, API endpoints, data models, and Supabase operations where applicable.

Remember that the base template only provides Shadcn UI components and basic Supabase client setup. You'll need to plan for authentication flows, database tables, and all other functionality required by the project.`;
};

const baseNextjsProject = `This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where content has been compressed (code blocks are separated by ⋮---- delimiter).

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Content has been compressed - code blocks are separated by ⋮---- delimiter
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
src/
  app/
    globals.css
    layout.tsx
    page.tsx
.gitignore
eslint.config.mjs
next.config.ts
package.json
postcss.config.mjs
tsconfig.json
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="src/app/globals.css">
:root {
⋮----
@theme inline {
⋮----
body {
</file>

<file path="src/app/layout.tsx">
import type { Metadata } from "next";
import { Geist, Geist_Mono } from "next/font/google";
⋮----
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>)
</file>

<file path="src/app/page.tsx">
export default function Home()
</file>

<file path=".gitignore">
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
</file>

<file path="eslint.config.mjs">
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
⋮----
const compat = new FlatCompat({
⋮----
...compat.extends("next/core-web-vitals", "next/typescript"),
</file>

<file path="next.config.ts">
import type { NextConfig } from "next";
⋮----
/* config options here */
</file>

<file path="postcss.config.mjs">

</file>

<file path="tsconfig.json">
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
</file>

<file path="package.json">
{
  "name": "sample",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "next": "15.3.2"
  },
  "devDependencies": {
    "typescript": "^5",
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "@tailwindcss/postcss": "^4",
    "tailwindcss": "^4",
    "eslint": "^9",
    "eslint-config-next": "15.3.2",
    "@eslint/eslintrc": "^3"
  },
  "packageManager": "pnpm@9.15.5+sha512.845196026aab1cc3f098a0474b64dfbab2afe7a1b4e91dd86895d8e4aa32a7a6d03049e2d0ad770bbe4de023a7122fb68c1a1d6e0d033c7076085f9d5d4800d4"
}
</file>

</files>`;
