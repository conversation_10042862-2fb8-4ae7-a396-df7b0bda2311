import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import * as express from 'express';
import { AppModule } from './app.module';

async function bootstrap() {
  // Disable the built-in body parser
  const app = await NestFactory.create(AppModule, {
    bodyParser: false,
  });

  // Apply a body parser with appropriate limits for different routes
  app.use((req, res, next) => {
    // Skip chat routes - they'll be handled by the chat module middleware
    if (req.originalUrl.includes('/api/chat')) {
      return next();
    }

    // Apply increased limits for GitHub API routes
    if (req.originalUrl.includes('/api/github')) {
      express.json({ limit: '10mb' })(req, res, next);
      return;
    }

    if (req.originalUrl.includes('/api/preview/upload')) {
      express.json({ limit: '10mb' })(req, res, next);
      return;
    }

    if (req.originalUrl.includes('/validation/files')) {
      express.json({ limit: '10mb' })(req, res, next);
      return;
    }

    // Apply default body parser for all other routes
    express.json()(req, res, next);
  });

  app.enableCors();
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );
  app.setGlobalPrefix('api');
  app.enableShutdownHooks();
  await app.listen(process.env.COREAPI_PORT || 9700);
  console.log(
    `Iammoderator Coreapi started on PORT ${process.env.COREAPI_PORT || 9700}`,
  );
}
bootstrap();
