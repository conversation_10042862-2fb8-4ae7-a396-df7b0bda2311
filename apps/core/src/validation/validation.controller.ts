import {
  Body,
  Controller,
  HttpException,
  HttpStatus,
  Logger,
  Post,
} from '@nestjs/common';
import { ValidateFilesDto } from './dto/validate-files.dto';
import { ProjectValidationResultDto } from './dto/validation-result.dto';
import { ValidationService } from './validation.service';

@Controller('validation')
export class ValidationController {
  private readonly logger = new Logger(ValidationController.name);

  constructor(private readonly validationService: ValidationService) {}

  @Post('files')
  async validateFiles(
    @Body() validateFilesDto: ValidateFilesDto,
  ): Promise<ProjectValidationResultDto> {
    try {
      this.logger.log(
        `Received validation request for ${Object.keys(validateFilesDto.files).length} files`,
      );

      const filesArray = Object.entries(validateFilesDto.files).map(
        ([path, content]) => ({ path, content }),
      );

      // Basic file validation
      this.validateFileSizes(filesArray);

      // Perform validation (simplified - no options needed)
      const result = await this.validationService.validateProject(filesArray);

      this.logger.log(
        `Validation completed: ${result.errors.length} errors found`,
      );

      return result;
    } catch (error) {
      this.logger.error('Validation request failed:', error);

      if (error instanceof HttpException) {
        throw error;
      }

      // Handle file size validation errors specifically
      if (
        error.message.includes('Too many files') ||
        error.message.includes('File too large') ||
        error.message.includes('Total file size too large')
      ) {
        throw new HttpException(
          {
            message: error.message,
            type: 'FILE_SIZE_ERROR',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      throw new HttpException(
        {
          message: 'Validation failed',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Simple file size validation
   */
  private validateFileSizes(
    files: Array<{ path: string; content: string }>,
  ): void {
    const MAX_FILES = 100;
    const MAX_FILE_SIZE = 1024 * 1024; // 1MB per file
    const MAX_TOTAL_SIZE = 10 * 1024 * 1024; // 10MB total

    if (files.length > MAX_FILES) {
      throw new Error(
        `Too many files: ${files.length}. Maximum allowed: ${MAX_FILES}`,
      );
    }

    let totalSize = 0;
    for (const file of files) {
      const fileSize = Buffer.byteLength(file.content, 'utf8');

      if (fileSize > MAX_FILE_SIZE) {
        throw new Error(
          `File too large: ${file.path} (${Math.round(fileSize / 1024)}KB). Maximum allowed: ${MAX_FILE_SIZE / 1024}KB`,
        );
      }

      totalSize += fileSize;
    }

    if (totalSize > MAX_TOTAL_SIZE) {
      throw new Error(
        `Total file size too large: ${Math.round(totalSize / 1024)}KB. Maximum allowed: ${MAX_TOTAL_SIZE / 1024}KB`,
      );
    }
  }
}
