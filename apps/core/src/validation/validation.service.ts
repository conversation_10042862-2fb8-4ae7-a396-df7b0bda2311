import { Injectable, Logger } from '@nestjs/common';
import { ESLint } from 'eslint';
import * as ts from 'typescript';
import type {
  ProjectValidationResult,
  ValidationError,
  ValidationFile,
} from './interfaces/validation.interface';

@Injectable()
export class ValidationService {
  private readonly logger = new Logger(ValidationService.name);

  async validateProject(
    files: ValidationFile[],
  ): Promise<ProjectValidationResult> {
    try {
      this.logger.log(`Starting validation for ${files.length} files`);

      if (!files || files.length === 0) {
        return this.createEmptyResult();
      }

      const allErrors: ValidationError[] = [];

      // Process each file with performance optimizations
      for (const file of files) {
        try {
          // Skip validation for large files (performance optimization)
          if (file.content.length > 50000) {
            this.logger.debug(
              `Skipping large file: ${file.path} (${file.content.length} chars)`,
            );
            continue;
          }

          // TypeScript validation (only for .ts/.tsx files)
          if (this.isTypeScriptFile(file.path)) {
            const tsErrors = this.validateTypeScript(file.path, file.content);
            allErrors.push(...tsErrors);
          }

          // ESLint validation for JS/TS/JSX/TSX files
          if (this.isLintableFile(file.path)) {
            const eslintErrors = await this.validateWithESLint(
              file.path,
              file.content,
            );
            allErrors.push(...eslintErrors);
          }
        } catch (error) {
          this.logger.error(`Error validating file ${file.path}:`, error);
          allErrors.push({
            line: 1,
            column: 1,
            message: `Validation failed: ${error.message}`,
            severity: 'error',
            source: 'system',
            filePath: file.path,
          });
        }
      }

      // Sort errors by file path, then by line number
      const sortedErrors = allErrors.sort((a, b) => {
        if (a.filePath !== b.filePath) {
          return (a.filePath || '').localeCompare(b.filePath || '');
        }
        return a.line - b.line;
      });

      const result: ProjectValidationResult = {
        isValid: sortedErrors.length === 0,
        errors: sortedErrors,
      };

      this.logger.log(
        `Validation completed - ${sortedErrors.length} errors found`,
      );

      return result;
    } catch (error) {
      this.logger.error('Validation failed:', error);
      throw error;
    }
  }

  /**
   * Validate TypeScript compilation errors (fast, minimal validation)
   */
  private validateTypeScript(
    fileName: string,
    sourceCode: string,
  ): ValidationError[] {
    try {
      const compilerOptions: ts.CompilerOptions = {
        target: ts.ScriptTarget.ES5, // Faster target
        module: ts.ModuleKind.CommonJS,
        jsx: ts.JsxEmit.React,
        strict: true, // Keep strict for basic error detection
        noEmit: true,
        allowJs: true,
        // Ultra-fast options
        noResolve: true, // Skip all module resolution
        isolatedModules: true, // Fast isolated checking
        skipLibCheck: true, // Skip all lib checking
        skipDefaultLibCheck: true, // Skip default lib
        // Disable most checks for speed
        noImplicitAny: false,
        noUnusedLocals: false,
        noUnusedParameters: false,
        noImplicitReturns: false,
        noImplicitThis: false,
        noImplicitOverride: false,
      };

      const sourceFile = ts.createSourceFile(
        fileName,
        sourceCode,
        ts.ScriptTarget.ES2020,
        true,
      );

      // Fast, minimal compiler host - only provide our file
      const host: ts.CompilerHost = {
        getSourceFile: (name, _languageVersion) => {
          // Only return our target file
          return name === fileName ? sourceFile : undefined;
        },
        writeFile: () => {},
        getCurrentDirectory: () => process.cwd(),
        getDirectories: () => [],
        fileExists: (name) => name === fileName,
        readFile: (name) => (name === fileName ? sourceCode : undefined),
        getCanonicalFileName: (fileName) => fileName,
        useCaseSensitiveFileNames: () => true,
        getNewLine: () => '\n',
        getDefaultLibFileName: (options) => ts.getDefaultLibFilePath(options),
        resolveModuleNames: () => [],
      };

      const program = ts.createProgram([fileName], compilerOptions, host);
      const diagnostics = ts.getPreEmitDiagnostics(program);

      return diagnostics
        .filter((diagnostic) => {
          // Only include diagnostics for the current file
          if (diagnostic.file?.fileName !== fileName) return false;

          // Filter out module resolution and environment-related errors
          // but keep all syntax, type, and logic errors within the code itself
          const environmentErrors = [
            2307, // Cannot find module 'X' or its corresponding type declarations
            2304, // Cannot find name 'React', 'window', 'document', etc. (missing globals)
            2503, // Cannot find namespace 'React' (missing type definitions)
            2584, // Cannot find name 'document' (DOM globals)
          ];

          // Keep all errors except environment-related ones
          return !environmentErrors.includes(diagnostic.code || 0);
        })
        .map((diagnostic) => {
          const { line, character } =
            diagnostic.file!.getLineAndCharacterOfPosition(diagnostic.start!);
          return {
            line: line + 1,
            column: character + 1,
            message: ts.flattenDiagnosticMessageText(
              diagnostic.messageText,
              '\n',
            ),
            severity: 'error' as const,
            source: 'typescript',
            code: `TS${diagnostic.code}`,
            filePath: fileName,
          };
        });
    } catch (error) {
      this.logger.warn(`TypeScript validation failed for ${fileName}:`, error);
      return [];
    }
  }

  /**
   * Validate with ESLint using Next.js configuration
   */
  private async validateWithESLint(
    fileName: string,
    sourceCode: string,
  ): Promise<ValidationError[]> {
    try {
      const eslint = new ESLint({
        baseConfig: {
          extends: ['next/core-web-vitals'],
          parser: '@typescript-eslint/parser',
          parserOptions: {
            ecmaVersion: 2020,
            sourceType: 'module',
            ecmaFeatures: { jsx: true },
            project: null, // Disable project-based linting for simplicity
          },
          env: {
            browser: true,
            es2020: true,
            node: true,
          },
          rules: {
            // Disable some strict rules that might be too harsh for AI-generated code
            '@typescript-eslint/no-unused-vars': 'off',
            '@typescript-eslint/no-explicit-any': 'off',
            'react-hooks/exhaustive-deps': 'warn',
            'react/no-unescaped-entities': 'off', // Disable apostrophe and quote escaping errors
          },
        },
        useEslintrc: false,
        overrideConfig: {
          ignorePatterns: [], // Don't ignore any files
        },
      });

      const results = await eslint.lintText(sourceCode, { filePath: fileName });

      return (
        results[0]?.messages.map((message) => ({
          line: message.line,
          column: message.column,
          message: message.message,
          severity:
            message.severity === 2 ? ('error' as const) : ('warning' as const),
          source: 'eslint',
          code: message.ruleId || 'eslint',
          ruleId: message.ruleId,
          filePath: fileName,
        })) || []
      );
    } catch (error) {
      this.logger.warn(`ESLint validation failed for ${fileName}:`, error);
      return [];
    }
  }

  /**
   * Check if file is a TypeScript file
   */
  private isTypeScriptFile(filePath: string): boolean {
    return filePath.endsWith('.ts') || filePath.endsWith('.tsx');
  }

  /**
   * Check if file should be linted
   */
  private isLintableFile(filePath: string): boolean {
    const lintableExtensions = ['.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs'];
    return lintableExtensions.some((ext) =>
      filePath.toLowerCase().endsWith(ext),
    );
  }

  /**
   * Create empty result for when no files are provided
   */
  private createEmptyResult(): ProjectValidationResult {
    return {
      isValid: true,
      errors: [],
    };
  }
}
