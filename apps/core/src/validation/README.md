# Validation Service

A simplified validation service for AI-generated Next.js code that focuses on essential error detection with clear line-by-line reporting.

## Overview

This service validates TypeScript/JavaScript files and provides:

- **TypeScript compilation errors** using the TypeScript Compiler API
- **Next.js ESLint rules** using `eslint-config-next`
- **Simple error reporting** with line numbers and messages

## Architecture

```
📁 validation/
├── 📄 validation.service.ts          # Main validation service (~240 lines)
├── 📄 validation.controller.ts       # HTTP API endpoints
├── 📄 validation.module.ts           # NestJS module
├── 📄 validation.service.spec.ts     # Unit tests
├── 📄 interfaces/
│   └── 📄 validation.interface.ts    # TypeScript interfaces
└── 📄 dto/
    ├── 📄 validate-files.dto.ts      # Request DTOs
    └── 📄 validation-result.dto.ts   # Response DTOs
```

## Key Features

### ✅ Simplified Design

- **Single service class** instead of multiple validators
- **Direct validation** without caching or parallel processing
- **~80% code reduction** from previous complex system

### ✅ Essential Validations

- **TypeScript errors**: Syntax errors, type errors, compilation issues
- **Next.js rules**: Best practices via `eslint-config-next`
- **File validation**: Size limits and basic checks

### ✅ Clear Error Reporting

- **Line numbers** and column positions
- **Error messages** with clear descriptions
- **Error sources** (TypeScript, ESLint, etc.)
- **Sorted output** by file and line number

## API Endpoints

### POST `/validation/files`

Validates multiple files and returns errors with line numbers.

**Request:**

```json
{
  "files": {
    "components/Button.tsx": "export default function Button() { return <button>Click me</button>; }",
    "pages/index.tsx": "import Button from '../components/Button'; export default function Home() { return <Button />; }"
  }
}
```

**Response:**

```json
{
  "isValid": false,
  "errors": [
    {
      "line": 1,
      "column": 45,
      "message": "Missing return type annotation",
      "severity": "error",
      "source": "typescript",
      "filePath": "components/Button.tsx"
    }
  ],
  "warnings": [],
  "performance": {
    "totalTime": 150,
    "filesProcessed": 2,
    "cacheHits": "0%"
  }
}
```

### GET `/validation/health`

Returns health status of the validation service.

**Response:**

```json
{
  "status": "healthy",
  "validators": {
    "typescript": true,
    "eslint": true
  },
  "timestamp": "2025-06-21T10:00:00.000Z"
}
```

## Usage Examples

### Basic Validation

```typescript
import { ValidationService } from './validation.service';

const validationService = new ValidationService();

const files = [
  {
    path: 'components/Button.tsx',
    content:
      'export default function Button() { return <button>Click</button>; }',
  },
];

const result = await validationService.validateProject(files);

if (!result.isValid) {
  result.errors.forEach((error) => {
    console.log(
      `${error.filePath}:${error.line}:${error.column} - ${error.message}`,
    );
  });
}
```

### TypeScript Configuration

The service uses these TypeScript compiler options:

```typescript
{
  target: ts.ScriptTarget.ES2020,
  module: ts.ModuleKind.ESNext,
  jsx: ts.JsxEmit.ReactJSX,
  strict: true,
  esModuleInterop: true,
  skipLibCheck: true,
  noEmit: true,
  allowJs: true,
  moduleResolution: ts.ModuleResolutionKind.NodeNext
}
```

### ESLint Configuration

Uses Next.js recommended configuration:

```typescript
{
  extends: ['next/core-web-vitals'],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: { jsx: true }
  }
}
```

## File Size Limits

- **Maximum files**: 100 files per request
- **Maximum file size**: 1MB per file
- **Maximum total size**: 10MB per request

## Supported File Types

### TypeScript Validation

- `.ts` - TypeScript files
- `.tsx` - TypeScript React files
- `.js` - JavaScript files (with TypeScript checking)
- `.jsx` - JavaScript React files

### ESLint Validation

- `.js`, `.jsx` - JavaScript files
- `.ts`, `.tsx` - TypeScript files
- `.mjs`, `.cjs` - Module JavaScript files

## Performance

- **Response time**: < 500ms for typical requests
- **Memory usage**: < 50MB baseline
- **Processing**: Sequential file validation
- **No caching**: Simplified for reliability

## Dependencies

### Core Dependencies

- `typescript` - TypeScript compiler API
- `eslint` - JavaScript/TypeScript linting
- `@typescript-eslint/parser` - TypeScript parser for ESLint
- `eslint-config-next` - Next.js ESLint configuration

### NestJS Dependencies

- `@nestjs/common` - NestJS framework
- Standard NestJS testing utilities

## Testing

Run the test suite:

```bash
npm test -- --testPathPattern=validation.service.spec.ts
```

Test coverage includes:

- Empty file validation
- TypeScript error detection
- React component validation
- Next.js specific rules
- Health check functionality

## Migration from Complex System

This simplified service replaced a complex multi-validator system with:

### Removed Components

- ❌ `SyntaxValidator` class
- ❌ `TypeScriptValidator` class
- ❌ `ESLintValidator` class
- ❌ `NextJSValidator` class
- ❌ `ValidationCacheService`
- ❌ `TypeScriptCompilerService`
- ❌ `FileUtils` and `ErrorUtils`
- ❌ Complex caching and batching logic
- ❌ Parallel processing
- ❌ Performance monitoring

### Benefits Achieved

- **80% code reduction**: From ~2000 lines to ~400 lines
- **Simpler maintenance**: Single service to manage
- **Better reliability**: Fewer moving parts
- **Faster development**: Less abstraction overhead
- **Clearer debugging**: Direct error paths

## Error Types

### TypeScript Errors

```typescript
{
  line: 5,
  column: 12,
  message: "Type 'string' is not assignable to type 'number'",
  severity: "error",
  source: "typescript",
  code: "TS2322"
}
```

### ESLint Errors

```typescript
{
  line: 8,
  column: 25,
  message: "Do not use <a>. Use Link from 'next/link' instead",
  severity: "error",
  source: "eslint",
  ruleId: "@next/next/no-html-link-for-pages"
}
```

## Troubleshooting

### Common Issues

**ESLint warnings about missing pages directory:**

- This is expected for isolated validation
- Warnings are logged but don't affect functionality

**React version detection warnings:**

- Service works without React installed
- Warnings are cosmetic only

**TypeScript module resolution:**

- Service skips complex module resolution
- Focuses on syntax and basic type checking

## Contributing

When making changes:

1. Keep the service simple and focused
2. Maintain backward API compatibility
3. Add tests for new validation rules
4. Update this README for significant changes

---

**Last Updated**: June 21, 2025  
**Version**: 2.0 (Simplified)
