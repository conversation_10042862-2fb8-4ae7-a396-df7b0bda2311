export interface ValidationFile {
  path: string;
  content: string;
}

export interface ValidationError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning';
  source: string;
  code?: string | number;
  ruleId?: string;
  filePath?: string;
}

export interface ValidationResult {
  file: string;
  type: 'syntax' | 'typescript' | 'nextjs' | 'eslint';
  errors: ValidationError[];
  warnings: ValidationError[];
  time: number;
}

export interface ProjectValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationOptions {
  enableTypeScript?: boolean;
  enableESLint?: boolean;
  enableNextJS?: boolean;
  enableSyntax?: boolean;
}

// Keep these for backward compatibility but mark as deprecated
/** @deprecated Not used in simplified validation */
export interface CacheInstance {
  cache: Map<string, ValidationResult & { timestamp: number }>;
  maxSize: number;
  hits: number;
  misses: number;
  getHitRate: () => string;
}

/** @deprecated Not used in simplified validation */
export interface PerformanceMetrics {
  totalTime: number;
  filesProcessed: number;
  cacheHits: string;
  memoryUsageKB?: number;
  efficiency?: number;
}

/** @deprecated Simplified in new validation service */
export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'error';
  timestamp: string;
  service: string;
  cache?: any;
  validators?: Record<string, boolean>;
  performance?: any;
}

export type ValidatorFunction = (file: ValidationFile) => ValidationResult;
export type MultiValidatorFunction = (
  files: ValidationFile[],
) => ValidationResult[];

export type ValidationType = 'syntax' | 'typescript' | 'eslint' | 'nextjs';
