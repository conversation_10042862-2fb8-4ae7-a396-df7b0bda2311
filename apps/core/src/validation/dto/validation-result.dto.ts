import type {
  ProjectValidationResult,
  ValidationError,
} from '../interfaces/validation.interface';

// DTOs now extend from interfaces to ensure consistency
export class ValidationErrorDto implements ValidationError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning';
  source: string;
  code?: string | number;
  ruleId?: string;
}

export class ProjectValidationResultDto implements ProjectValidationResult {
  isValid: boolean;
  errors: ValidationErrorDto[];
}
