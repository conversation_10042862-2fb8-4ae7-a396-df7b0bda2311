import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsObject,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import type { ValidationOptions } from '../interfaces/validation.interface';

export class ValidationOptionsDto implements ValidationOptions {
  @IsBoolean()
  @IsOptional()
  enableTypeScript?: boolean = true;

  @IsBoolean()
  @IsOptional()
  enableESLint?: boolean = true;

  @IsBoolean()
  @IsOptional()
  enableNextJS?: boolean = true;

  @IsBoolean()
  @IsOptional()
  enableSyntax?: boolean = true;
}

export class ValidateFilesDto {
  @IsObject()
  files: Record<string, string>;

  @IsOptional()
  @ValidateNested()
  @Type(() => ValidationOptionsDto)
  options?: ValidationOptionsDto;
}
