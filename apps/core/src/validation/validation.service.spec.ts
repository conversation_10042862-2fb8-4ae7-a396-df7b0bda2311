import { Test, TestingModule } from '@nestjs/testing';
import { _hardCodedNextjsRepo } from './hardcoded/_hardcoded-nextjs-repo';
import { ValidationService } from './validation.service';

describe('ValidationService', () => {
  let service: ValidationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ValidationService],
    }).compile();

    service = module.get<ValidationService>(ValidationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // describe('validateProject', () => {
  //   it('should return valid result for empty files', async () => {
  //     const result = await service.validateProject([]);

  //     expect(result.isValid).toBe(true);
  //     expect(result.errors).toHaveLength(0);
  //     expect(result.warnings).toHaveLength(0);
  //     expect(result.performance.filesProcessed).toBe(0);
  //   });

  //   it('should validate simple TypeScript file without errors', async () => {
  //     const files = [
  //       {
  //         path: 'test.ts',
  //         content: 'const x: number = 1;',
  //       },
  //     ];

  //     const result = await service.validateProject(files);

  //     expect(result.performance.filesProcessed).toBe(1);
  //     expect(result.errors.length).toBeLessThanOrEqual(0); // Should have no errors for simple valid code
  //   });

  //   it('should detect TypeScript errors', async () => {
  //     const files = [
  //       {
  //         path: 'test.ts',
  //         content: 'const x: number = "string"; // Type error',
  //       },
  //     ];

  //     const result = await service.validateProject(files);

  //     expect(result.performance.filesProcessed).toBe(1);
  //     expect(result.isValid).toBe(false);
  //     expect(result.errors.length).toBeGreaterThan(0);
  //     expect(result.errors[0].source).toBe('typescript');
  //   });

  //   it('should detect TypeScript variable reassign errors', async () => {
  //     const files = [
  //       {
  //         path: 'test.ts',
  //         content: `
  //           const x: number = 1;
  //           x = 2; // Reassignment error
  //         `,
  //       },
  //     ];

  //     const result = await service.validateProject(files);

  //     expect(result.performance.filesProcessed).toBe(1);
  //     expect(result.isValid).toBe(false);
  //     expect(result.errors.length).toBeGreaterThan(0);
  //     expect(result.errors[0].source).toBe('typescript');
  //   });

  //   it('should validate React component', async () => {
  //     const files = [
  //       {
  //         path: 'component.tsx',
  //         content: `
  //           import React from 'react';

  //           export default function TestComponent() {
  //             return <div>Hello World</div>;
  //           }
  //         `,
  //       },
  //     ];

  //     const result = await service.validateProject(files);

  //     expect(result.performance.filesProcessed).toBe(1);
  //   });

  //   it('should detect Next.js specific issues', async () => {
  //     const files = [
  //       {
  //         path: 'pages/test.tsx',
  //         content: `
  //           import Link from 'next/link';

  //           export default function Page() {
  //             return <a href="/test">Bad Link</a>; // Should use Link component
  //           }
  //         `,
  //       },
  //     ];

  //     const result = await service.validateProject(files);

  //     expect(result.performance.filesProcessed).toBe(1);
  //   });
  // });

  //   describe('syntax-only validation', () => {
  //     it('should detect actual syntax errors while ignoring module imports', async () => {
  //       const result = await service.validateProject([
  //         {
  //           path: 'test.tsx',
  //           content: `
  // import React from 'react';
  // import { Button } from '@/components/ui/button';

  // export default function TestComponent() {
  //   let test = 23;
  //   test = 42; // This should be caught - const reassignment
  //   return <div>Test</div>;
  // }
  //           `,
  //         },
  //       ]);

  //       expect(result.isValid).toBe(false);
  //       expect(result.errors.length).toBeGreaterThan(0);
  //       expect(result.errors[0].message).toContain('Cannot assign to');
  //     });
  //   });

  describe('validate full hardcoded project', () => {
    it('should validate a full hardcoded project with multiple files', async () => {
      const files = Object.keys(_hardCodedNextjsRepo.files).map((path) => ({
        path,
        content: _hardCodedNextjsRepo.files[path],
      }));
      const result = await service.validateProject([
        ...files,
        // {
        //   path: 'src/app/page.tsx',
        //   content:
        //     'export default function Home() {\n const test = 23; test = 43;   return <div>App home page</div>;\n}\n',
        // },
        // {
        //   path: 'src/app/page.tsx',
        //   content:
        //     'export default function Home() {\n' +
        //     ' const test = 23; test = 43;   return <div>App home page</div>;\n' +
        //     '}\n',
        // },
      ]);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
      expect(result.performance.filesProcessed).toBe(files.length);
    });
  });
});
