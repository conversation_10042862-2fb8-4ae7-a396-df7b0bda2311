import { Injectable } from '@nestjs/common';
import * as swc from '@swc/core';
import * as crypto from 'crypto';
import * as esbuild from 'esbuild';
import * as fs from 'fs/promises';
import * as os from 'os';
import * as path from 'path';

interface DirectoryContent {
  [key: string]: {
    file?: { contents: string };
    directory?: DirectoryContent;
  };
}

interface CompileRequest {
  compile: DirectoryContent;
}

@Injectable()
export class ReactCompilerService {
  private async createTempDirectory(): Promise<string> {
    const tempDir = path.join(
      os.tmpdir(),
      `react-compile-${crypto.randomBytes(8).toString('hex')}`,
    );
    await fs.mkdir(tempDir, { recursive: true });
    return tempDir;
  }

  private async writeFiles(directory: string, files: DirectoryContent) {
    for (const [name, content] of Object.entries(files)) {
      const fullPath = path.join(directory, name);
      if (content.file?.contents) {
        await fs.writeFile(fullPath, content.file.contents, 'utf-8');
      } else if (content.directory) {
        await fs.mkdir(fullPath, { recursive: true });
        await this.writeFiles(fullPath, content.directory);
      }
    }
  }

  private async compileTSFileWithSWC(filePath: string): Promise<void> {
    const source = await fs.readFile(filePath, 'utf-8');
    const result = await swc.transform(source, {
      filename: filePath,
      jsc: {
        parser: {
          syntax: filePath.endsWith('.tsx') ? 'typescript' : 'ecmascript',
          tsx: filePath.endsWith('.tsx'),
        },
        target: 'es2020',
      },
      module: { type: 'es6' },
    });
    await fs.writeFile(
      filePath.replace(/\.tsx?$/, '.jsx'),
      result.code,
      'utf-8',
    );
  }

  private async generateImportMap(
    dependencies: Record<string, string>,
  ): Promise<Record<string, string>> {
    const importMap: Record<string, string> = {
      react: 'https://esm.sh/react@18.2.0',
      'react-dom': 'https://esm.sh/react-dom@18.2.0',
      'react-dom/client': 'https://esm.sh/react-dom@18.2.0/client',
    };

    const knownSubpaths: Record<string, string[]> = {
      '@hookform/resolvers': ['/zod'],
      '@radix-ui/react-label': ['/'],
      '@radix-ui/react-toast': ['/'],
      'react-hook-form': ['/'],
      zod: ['/'],
      clsx: ['/'],
    };

    await Promise.all(
      Object.entries(dependencies).map(async ([dep, version]) => {
        if (!importMap[dep]) {
          const cleanVersion = version.replace(/[\^~>=<]/g, '');
          const url = `https://esm.sh/${dep}@${cleanVersion}`;
          importMap[dep] = url;
          if (knownSubpaths[dep]) {
            for (const sub of knownSubpaths[dep]) {
              importMap[`${dep}${sub}`] = `${url}${sub}`;
            }
          }
        }
      }),
    );

    return importMap;
  }

  private async processCSSFile(filePath: string): Promise<string> {
    try {
      return await fs.readFile(filePath, 'utf-8');
    } catch {
      return '';
    }
  }

  private async bundleWithEsbuild(
    directory: string,
    importMap: Record<string, string>,
  ): Promise<string> {
    const distDir = path.join(directory, 'dist');
    await fs.mkdir(distDir, { recursive: true });
    await esbuild.build({
      entryPoints: [path.join(directory, 'src/main.jsx')],
      bundle: true,
      format: 'esm',
      platform: 'browser',
      target: 'es2020',
      outfile: path.join(distDir, 'bundle.js'),
      loader: {
        '.js': 'jsx',
        '.jsx': 'jsx',
      },
      define: {
        'process.env.NODE_ENV': '"production"',
      },
      external: Object.keys(importMap),
    });

    return path.join(distDir, 'bundle.js');
  }

  async compileProject(request: CompileRequest): Promise<string> {
    const tempDir = await this.createTempDirectory();

    try {
      await this.writeFiles(tempDir, request.compile);

      const srcDir = path.join(tempDir, 'src');
      const files = await fs.readdir(srcDir);
      let cssContent = '';

      for (const file of files) {
        const filePath = path.join(srcDir, file);

        if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          await this.compileTSFileWithSWC(filePath);
        } else if (file.endsWith('.css')) {
          cssContent += (await this.processCSSFile(filePath)) + '\n';
        }
      }

      let importMap: Record<string, string> = {};
      const packageJsonPath = path.join(tempDir, 'package.json');

      if (
        await fs
          .access(packageJsonPath)
          .then(() => true)
          .catch(() => false)
      ) {
        const packageJson = JSON.parse(
          await fs.readFile(packageJsonPath, 'utf-8'),
        );
        const dependencies = {
          ...packageJson.dependencies,
          ...packageJson.devDependencies,
        };
        importMap = await this.generateImportMap(dependencies);
      }

      const bundlePath = await this.bundleWithEsbuild(tempDir, importMap);
      const bundleContent = await fs.readFile(bundlePath, 'utf-8');

      const html = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="UTF-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1.0" />
            <style>${cssContent}</style>
            <script type="importmap">
              ${JSON.stringify({ imports: importMap }, null, 2)}
            </script>
          </head>
          <body>
            <div id="root"></div>
            <script type="module">
              ${bundleContent}
            </script>
          </body>
        </html>
      `;

      return html;
    } catch (err) {
      console.error('Compile error:', err);
      throw err;
    } finally {
      await fs.rm(tempDir, { recursive: true, force: true });
    }
  }
}
