import { Body, Controller, HttpStatus, Post, Res } from '@nestjs/common';
import { Response } from 'express';
import { ReactCompilerService } from './react-compiler.service';

interface CompileRequest {
  compile: {
    [key: string]: any;
  };
}

@Controller('react-compiler')
export class ReactCompilerController {
  constructor(private readonly reactCompilerService: ReactCompilerService) {}

  @Post('compile')
  async compile(@Body() request: CompileRequest, @Res() res: Response) {
    try {
      const html = await this.reactCompilerService.compileProject(request);
      res.header('Content-Type', 'text/html');
      return res.send(html);
    } catch (error) {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).send(error.message);
    }
  }
}
