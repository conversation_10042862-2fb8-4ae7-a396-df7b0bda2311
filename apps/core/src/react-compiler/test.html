<!doctype html>
<html>
  <head>
    <title>React Compiler Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }
      .container {
        display: flex;
        gap: 20px;
      }
      .editor {
        flex: 1;
      }
      .preview {
        flex: 1;
      }
      textarea {
        width: 100%;
        height: 600px;
        font-family: monospace;
      }
      iframe {
        width: 100%;
        height: 600px;
        border: 1px solid #ccc;
      }
      button {
        padding: 10px 20px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin: 10px 0;
      }
      button:hover {
        background: #0056b3;
      }
    </style>
  </head>
  <body>
    <h1>React Compiler Test</h1>
    <div class="container">
      <div class="editor">
        <h2>Input</h2>
        <textarea
          id="input"
          placeholder="Paste your React project JSON here..."
        ></textarea>
        <button onclick="compile()">Compile</button>
      </div>
      <div class="preview">
        <h2>Preview</h2>
        <iframe id="preview"></iframe>
      </div>
    </div>

    <script>
      async function compile() {
        try {
          const input = document.getElementById('input').value;
          const jsonData = JSON.parse(input);

          const response = await fetch(
            'http://localhost:9700/api/react-compiler/compile',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(jsonData),
            },
          );

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const html = await response.text();
          const preview = document.getElementById('preview');
          preview.srcdoc = html;
        } catch (error) {
          console.error('Error:', error);
          alert('Error: ' + error.message);
        }
      }
    </script>
  </body>
</html>
