import { IsObject, IsString } from 'class-validator';

// Helper class for validating Record<string, string>
// This is a common pattern, but class-validator doesn't have a direct decorator for Record<string, string>
// where keys are dynamic but values must be strings.
// A simpler approach for now is to trust the input structure or use a more generic IsObject.
// For more robust validation, a custom validator or iterating keys might be needed.

export class WriteFilesDto {
  @IsObject()
  // Add further validation if necessary, e.g., ensuring all values in the object are strings.
  // For now, @IsObject checks that it's an object.
  files: Record<string, string>;

  @IsString()
  targetDir: string;
}
