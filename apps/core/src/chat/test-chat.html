<!doctype html>
<html>
  <head>
    <title>Chat Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      #chat-container {
        border: 1px solid #ccc;
        padding: 20px;
        height: 400px;
        overflow-y: auto;
        margin-bottom: 20px;
      }
      #message-input {
        width: 100%;
        padding: 10px;
        margin-bottom: 10px;
      }
      button {
        padding: 10px 20px;
        background: #007bff;
        color: white;
        border: none;
        cursor: pointer;
      }
      .message {
        margin: 10px 0;
        padding: 10px;
        border-radius: 5px;
      }
      .user-message {
        background: #e3f2fd;
        margin-left: 20%;
      }
      .ai-message {
        background: #f5f5f5;
        margin-right: 20%;
      }
      .error-message {
        background: #ffebee;
        color: #c62828;
        text-align: center;
        margin: 10px 0;
        padding: 10px;
        border-radius: 5px;
      }
      #debug {
        margin-top: 20px;
        padding: 10px;
        background: #f5f5f5;
        border-radius: 5px;
        font-family: monospace;
        white-space: pre-wrap;
      }
    </style>
  </head>
  <body>
    <h1>Chat Test</h1>
    <div id="chat-container"></div>
    <input type="text" id="message-input" placeholder="Type your message..." />
    <button onclick="sendMessage()">Send</button>
    <div id="debug"></div>

    <script>
      const chatContainer = document.getElementById('chat-container');
      const messageInput = document.getElementById('message-input');
      const debugDiv = document.getElementById('debug');
      let eventSource = null;
      const API_BASE_URL = 'http://localhost:9700/api';

      function logDebug(message) {
        debugDiv.textContent += `${new Date().toISOString()}: ${message}\n`;
        debugDiv.scrollTop = debugDiv.scrollHeight;
      }

      function addMessage(content, isUser = false, isError = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : isError ? 'error-message' : 'ai-message'}`;
        messageDiv.textContent = content;
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }

      function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;

        // Add user message to chat
        addMessage(message, true);
        messageInput.value = '';

        // Close previous connection if exists
        if (eventSource) {
          eventSource.close();
          logDebug('Closed previous connection');
        }

        try {
          logDebug(
            `Creating new connection to ${API_BASE_URL}/chat?message=${encodeURIComponent(message)}`,
          );
          // Create new SSE connection with proper URL
          eventSource = new EventSource(
            `${API_BASE_URL}/chat?message=${encodeURIComponent(message)}`,
          );

          eventSource.onopen = () => {
            logDebug('Connection opened');
          };

          eventSource.onmessage = (event) => {
            try {
              logDebug(`Received message: ${event.data}`);
              const data = JSON.parse(event.data);
              if (data.delta) {
                // Update the last AI message or create new one
                const lastMessage = chatContainer.lastElementChild;
                if (
                  lastMessage &&
                  !lastMessage.classList.contains('user-message') &&
                  !lastMessage.classList.contains('error-message')
                ) {
                  lastMessage.textContent += data.delta;
                } else {
                  addMessage(data.delta);
                }
              }
            } catch (e) {
              logDebug(`Error parsing message: ${e.message}`);
              addMessage('Error: Invalid response format', false, true);
            }
          };

          eventSource.onerror = (error) => {
            logDebug(`EventSource error: ${error.type}`);
            eventSource.close();
            addMessage(
              'Error: Connection lost. Please try again.',
              false,
              true,
            );
          };
        } catch (e) {
          logDebug(`Error creating EventSource: ${e.message}`);
          addMessage(
            'Error: Could not establish connection. Make sure the server is running.',
            false,
            true,
          );
        }
      }

      // Allow sending message with Enter key
      messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          sendMessage();
        }
      });
    </script>
  </body>
</html>
