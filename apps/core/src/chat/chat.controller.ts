import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '@shared-library/guard/jwt.guard';
import { ApiRequest } from '@shared-library/modules/auth/interfaces/jwt-payload.interface';
import { Response } from 'express';
import { ProjectPlanningService } from '../coreapi/project-planning/project-planning.service';
import { ChatService } from './chat.service';

interface ChatRequest {
  messages: any[];
  currentFiles: Record<string, string>;
  onboardingData?: Record<string, any> | null;
}
@Controller({
  path: 'chat',
})
export class ChatController {
  constructor(
    private readonly chatService: ChatService,
    private readonly projectPlanningService: ProjectPlanningService,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async chat(@Body() chatRequest: ChatRequest, @Res() res: Response) {
    try {
      // Set up SSE headers
      this.setupSSEHeaders(res);

      // Send status 200 immediately
      res.status(HttpStatus.OK);

      // Let the service handle streaming
      return await this.chatService.streamText(
        chatRequest.messages,
        chatRequest.currentFiles,
        chatRequest.onboardingData,
        res,
      );
    } catch (error) {
      return this.handleChatError(error, res);
    }
  }

  @Post('generate-blueprint')
  @UseGuards(JwtAuthGuard)
  async generateBlueprint(
    @Body() chatRequest: ChatRequest,
    @Res() res: Response,
  ) {
    try {
      // Set up SSE headers
      this.setupSSEHeaders(res);

      // Send status 200 immediately
      res.status(HttpStatus.OK);

      // Extract onboarding config from the chat message
      const lastMessage = chatRequest.messages[chatRequest.messages.length - 1];
      let onboardingConfig;

      try {
        // Try to parse the onboarding config from the message content
        const parsed = JSON.parse(lastMessage.content);
        onboardingConfig = parsed.onboardingConfig || parsed;
      } catch {
        // If parsing fails, use a default config
        onboardingConfig = {
          data: {
            name: 'Sample App',
            appDescription: 'A sample application',
            targetCustomers: 'General users',
            brandVoice: 'Professional',
            competitors: 'None specified',
          },
        };
      }

      // Let the service handle streaming
      return await this.chatService.streamBlueprintGeneration(
        onboardingConfig,
        res,
      );
    } catch (error) {
      return this.handleChatError(error, res);
    }
  }

  @Post('workspaces/:workspaceId/modules/:moduleId/pages/:pageId')
  @UseGuards(JwtAuthGuard)
  async chatWithHistory(
    @Param('workspaceId') workspaceId: string,
    @Param('moduleId') moduleId: string,
    @Param('pageId') pageId: string,
    @Body() chatRequest: ChatRequest,
    @Req() req: ApiRequest,
    @Res() res: Response,
  ) {
    try {
      const userId = req.user.userId;
      this.setupSSEHeaders(res);
      res.status(HttpStatus.OK);

      return await this.chatService.streamTextWithHistory(
        chatRequest.messages,
        chatRequest.currentFiles,
        chatRequest.onboardingData,
        workspaceId,
        moduleId,
        pageId,
        userId,
        res,
      );
    } catch (error) {
      return this.handleChatError(error, res);
    }
  }

  @Get('workspaces/:workspaceId/modules/:moduleId/pages/:pageId/history')
  @UseGuards(JwtAuthGuard)
  async getChatHistory(
    @Param('workspaceId') workspaceId: string,
    @Param('moduleId') moduleId: string,
    @Param('pageId') pageId: string,
  ) {
    return this.chatService.getChatHistory(workspaceId, moduleId, pageId);
  }

  private setupSSEHeaders(res: Response): void {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
  }

  private handleChatError(error: any, res: Response) {
    console.error('Chat error:', error);

    if (!res.headersSent) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to process chat request',
        message: error.message || 'Unknown error',
      });
    } else {
      // If headers are already sent, just end the response
      res.end();
    }
  }
}
