import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Logger,
  Post,
  Query,
} from '@nestjs/common';
import { GitHubService } from '@shared-library/modules/github/github.service';

interface PushToGitHubDto {
  workspaceId: string;
  files: Record<string, string>;
  commitMessage?: string;
  squash: boolean;
  commits?: Array<{ message: string; timestamp: number }>;
}

interface PullFromGitHubDto {
  workspaceId: string;
  path?: string;
  recursive?: boolean;
}

@Controller('github')
export class GithubController {
  private readonly logger = new Logger(GithubController.name);

  constructor(private readonly githubService: GitHubService) {}

  @Post('push')
  async pushToGitHub(@Body() dto: PushToGitHubDto) {
    try {
      this.logger.log(`Pushing to GitHub for workspace ${dto.workspaceId}`);

      if (!dto.workspaceId) {
        throw new HttpException(
          'Workspace ID is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (Object.keys(dto.files).length === 0) {
        throw new HttpException('No files to push', HttpStatus.BAD_REQUEST);
      }

      // For multi-commit approach, we need commits
      if (!dto.squash && (!dto.commits || dto.commits.length === 0)) {
        throw new HttpException(
          'Commits are required for non-squashed push',
          HttpStatus.BAD_REQUEST,
        );
      }

      // For squashed approach, we need a commit message
      const commitMessage = dto.commitMessage || 'Initial commit';

      const repoUrl = await this.githubService.pushProject(
        dto.workspaceId,
        dto.files,
        commitMessage,
        dto.squash,
        dto.commits,
      );

      return { success: true, repoUrl };
    } catch (error) {
      this.logger.error(`Failed to push to GitHub: ${error.message}`);
      throw new HttpException(
        `Failed to push to GitHub: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('pull')
  async pullFromGitHub(
    @Query('workspaceId') workspaceId: string,
    @Query('path') path?: string,
    @Query('recursive') recursive?: string,
  ) {
    try {
      this.logger.log(`Pulling code from GitHub for workspace ${workspaceId}`);

      if (!workspaceId) {
        throw new HttpException(
          'Workspace ID is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Convert recursive string parameter to boolean
      const isRecursive = recursive !== 'false';

      const files = await this.githubService.pullRepository(
        workspaceId,
        path || '',
        isRecursive,
      );

      return {
        success: true,
        workspaceId,
        fileCount: Object.keys(files).length,
        files,
      };
    } catch (error) {
      this.logger.error(`Failed to pull from GitHub: ${error.message}`);
      throw new HttpException(
        `Failed to pull from GitHub: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
