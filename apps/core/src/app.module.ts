import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { JwtGuardModule } from '@shared-library/guard/jwt.module';
import { TwoFactorGuardModule } from '@shared-library/guard/two-factor-guard.module';
import { FileModule } from '@shared-library/modules/file/file.module';
import { SupabaseModule } from '@shared-library/modules/supabase/supabase.module';
import { VercelModule } from '@shared-library/modules/vercel/vercel.module';
import * as path from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ChatModule } from './chat/chat.module';
import { CoreAuthModule } from './coreapi/auth/auth.module';
import { CoreFileModule } from './coreapi/file/file.module';
import { CoreOnboardingModule } from './coreapi/onboarding/onboarding.module';
import { ProjectPlanningModule } from './coreapi/project-planning/project-planning.module';
import { RepomixModule } from './coreapi/repomix/repomix.module';
import { CoreWorkspaceModule } from './coreapi/workspace/workspace.module';
import { CoreGithubModule } from './github/github.module';
import { MarkdownProcessorModule } from './markdown-processor/markdown-processor.module';
import { PreviewModule } from './preview/preview.module';
import { ReactCompilerModule } from './react-compiler/react-compiler.module';
import { ValidationModule } from './validation/validation.module';

@Module({
  imports: [
    CoreAuthModule,
    CoreWorkspaceModule,
    CoreOnboardingModule,
    ProjectPlanningModule,
    PreviewModule,
    JwtGuardModule,
    TwoFactorGuardModule,
    ConfigModule.forRoot({
      envFilePath: [
        // First check for local .env files
        path.resolve(process.cwd(), '.env'),
        // Then check for root .env files (dev mode)
        path.resolve(process.cwd(), '../../.env'),
      ],
      isGlobal: true,
    }),
    FileModule,
    CoreFileModule,
    EventEmitterModule.forRoot(),
    MarkdownProcessorModule,
    BullModule.forRoot({
      redis: {
        host: 'localhost',
        port: 6379,
      },
    }),
    ReactCompilerModule,
    ChatModule,
    CoreGithubModule,
    RepomixModule,
    ValidationModule,
    SupabaseModule,
    VercelModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
