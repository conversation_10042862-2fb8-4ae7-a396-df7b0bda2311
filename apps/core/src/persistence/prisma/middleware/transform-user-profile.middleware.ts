import { Prisma } from '@prisma/client';

export function transformUserProfile(): Prisma.Middleware {
  return async (params, next) => {
    if (params.model === 'UserProfile' && params.action === 'findMany') {
      const result = await next(params);
      return result.reduce((acc, profile) => {
        acc[profile.attribute] = profile.value;
        return acc;
      }, {});
    }
    return next(params);
  };
}
