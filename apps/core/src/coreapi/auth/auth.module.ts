import { Modu<PERSON> } from '@nestjs/common';
import { AuthModule } from '@shared-library/modules/auth/auth.module';
import { AuthController } from './auth.controller';
import { JwtGuardModule } from '@shared-library/guard/jwt.module';
import { RefreshTokenGuardModule } from '@shared-library/guard/refresh-token.module';
import { TwoFactorGuardModule } from '@shared-library/guard/two-factor-guard.module';

@Module({
  imports: [
    AuthModule,
    JwtGuardModule,
    RefreshTokenGuardModule,
    TwoFactorGuardModule,
  ],
  controllers: [AuthController],
})
export class CoreAuthModule {}
