import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Post,
  Put,
  Query,
  Req,
  Res,
  UnprocessableEntityException,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '@shared-library/guard/jwt.guard';
import { RefreshTokenGuard } from '@shared-library/guard/refresh-token.guard';
import { TwoFactorGuard } from '@shared-library/guard/two-factor.guard';
import { AuthService } from '@shared-library/modules/auth/auth.service';
import { ApiRequest } from '@shared-library/modules/auth/interfaces/jwt-payload.interface';
import { GoogleAuthService } from '@shared-library/modules/auth/oauth/auth-google.service';
import { Response } from 'express';
import { ChangePasswordDTO } from './dto/change-password.dto';
import { EmailLoginDTO } from './dto/email-login.dto';
import { UserSignupDTO } from './dto/user-signup.dto';
import { VerifyEmailDTO } from './dto/verify-email.dto';

@Controller({
  path: 'auth',
})
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly googleAuthService: GoogleAuthService,
  ) {}

  @Get()
  getHello(): string {
    return this.authService.getHello();
  }

  @HttpCode(HttpStatus.OK)
  @Post('login')
  async login(@Body() data: EmailLoginDTO): Promise<any> {
    return await this.authService.loginWithEmail(data);
  }

  @HttpCode(HttpStatus.CREATED)
  @Post('signup')
  async signup(@Body() data: UserSignupDTO): Promise<any> {
    const user = await this.authService.getUserByEmail(data.email);
    if (user)
      return new NotFoundException(
        `User with email ${user.email} already exists`,
      );

    return await this.authService.signup(data);
  }

  @HttpCode(HttpStatus.OK)
  @Post('verify')
  async verify(@Body() data: VerifyEmailDTO): Promise<any> {
    return await this.authService.verifyEmail(data);
  }

  @HttpCode(HttpStatus.OK)
  @Post('verify/resend')
  async resendVerificationCode(
    @Body() data: Partial<VerifyEmailDTO>,
  ): Promise<any> {
    return await this.authService.resendVerificationCode(data);
  }

  @HttpCode(HttpStatus.OK)
  @Get('me')
  @UseGuards(JwtAuthGuard)
  async getUser(@Req() req: ApiRequest): Promise<any> {
    const me = await this.authService.me(req.user.userId);
    return {
      message: 'User retrieved successfully',
      data: me,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Get('refresh')
  @UseGuards(RefreshTokenGuard)
  async refreshToken(@Req() req: ApiRequest) {
    const user = await this.authService.findUser(req.user.userId);
    if (!user) throw new NotFoundException(`User not found`);

    const { accessToken, refreshToken } =
      await this.authService.generateTokens(user);
    return {
      message: 'Tokens refreshed successfully',
      data: { accessToken, refreshToken },
    };
  }

  @HttpCode(HttpStatus.OK)
  @Put('change-password')
  @UseGuards(JwtAuthGuard)
  async changePassword(
    @Req() req: ApiRequest,
    @Body() data: ChangePasswordDTO,
  ) {
    const user = await this.authService.findUser(req.user.userId);
    if (!user) throw new NotFoundException(`User not found`);

    await this.authService.changePassword(user, data);
    return {
      message: 'Password changed successfully',
      data: req.user,
    };
  }

  @HttpCode(HttpStatus.ACCEPTED)
  @Post('logout')
  @UseGuards(JwtAuthGuard)
  async logout(
    @Req() req: ApiRequest,
    @Body() data: { fromAllDevices: boolean },
  ) {
    const user = await this.authService.findUser(req.user.userId);
    if (!user) throw new NotFoundException(`User not found`);

    if (data.fromAllDevices) await this.authService.logoutAllDevices(user);
    return {
      message: 'Logged out successfully',
    };
  }

  @HttpCode(HttpStatus.OK)
  @Get('two-factor-auth')
  @UseGuards(JwtAuthGuard)
  async getTwoFactoQRCode(@Req() req: ApiRequest) {
    const user = await this.authService.findUser(req.user.userId);
    if (!user) throw new NotFoundException(`User not found`);

    return await this.authService.getTwoFactorAuthQRCode(user);
  }

  @HttpCode(HttpStatus.OK)
  @Post('two-factor-auth/enable')
  @UseGuards(JwtAuthGuard)
  async enableTwoFactorAuth(
    @Req() req: ApiRequest,
    @Body() data: { code: string },
  ) {
    if (!data.code) throw new UnprocessableEntityException('Code is required');

    const user = await this.authService.findUser(req.user.userId);
    if (!user) throw new NotFoundException(`User not found`);

    return await this.authService.enableTwoFactorAuth(user, data.code);
  }

  @HttpCode(HttpStatus.OK)
  @Get('two-factor-auth/disable')
  @UseGuards(JwtAuthGuard)
  async disableTwoFactorAuth(@Req() req: ApiRequest) {
    const user = await this.authService.findUser(req.user.userId);
    if (!user) throw new NotFoundException(`User not found`);

    await this.authService.disableTwoFactorAuth(user);
    return {
      message: 'Two-factor authentication disabled successfully',
    };
  }

  @HttpCode(HttpStatus.OK)
  @Post('two-factor-auth/verify')
  @UseGuards(TwoFactorGuard)
  async verifyTwoFactorAuth(
    @Req() req: ApiRequest,
    @Body() data: { code: string },
  ) {
    if (!data.code) throw new UnprocessableEntityException('Code is required');
    const user = await this.authService.findUser(req.user.userId);
    if (!user) throw new NotFoundException(`User not found`);

    return await this.authService.verifyTwoFactorAuth(user, data.code);
  }

  @HttpCode(HttpStatus.OK)
  @Post('two-factor-auth/recovery-codes')
  @UseGuards(TwoFactorGuard)
  async loginWithRecoveryCode(
    @Req() req: ApiRequest,
    @Body() data: { recoveryCode: string },
  ) {
    if (!data.recoveryCode)
      throw new UnprocessableEntityException('Recovery code is required');
    const user = await this.authService.findUser(req.user.userId);
    if (!user) throw new NotFoundException(`User not found`);

    return await this.authService.loginWithRecoveryCode(
      user,
      data.recoveryCode,
    );
  }

  @HttpCode(HttpStatus.OK)
  @Get('google')
  async googleLogin(@Res() res: Response) {
    const url = this.googleAuthService.getAuthorizationUrl();
    return res.redirect(url);
  }

  @HttpCode(HttpStatus.OK)
  @Get('google/callback')
  async googleCallback(@Query('code') code: string, @Req() req: Request) {
    return await this.googleAuthService.handleCallback(code);
  }
}
