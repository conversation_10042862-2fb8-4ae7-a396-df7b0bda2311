import { <PERSON><PERSON><PERSON>, <PERSON>N<PERSON>Empt<PERSON>, Is<PERSON><PERSON>, Valida<PERSON> } from 'class-validator';
import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

/**
 * Custom validator to ensure password and passwordConfirmation match.
 */
@ValidatorConstraint({ name: 'MatchPassword', async: false })
export class MatchPasswordConstraint implements ValidatorConstraintInterface {
  validate(passwordConfirmation: string, args: ValidationArguments): boolean {
    const { password } = args.object as any;
    return password === passwordConfirmation;
  }

  defaultMessage(args: ValidationArguments): string {
    return 'Password confirmation does not match the password.';
  }
}

export class UserSignupDTO {
  @IsEmail({}, { message: 'Invalid email address format.' })
  @IsNotEmpty({ message: 'Email address is required.' })
  readonly email: string;

  @IsString({ message: 'Password must be a string.' })
  @IsNotEmpty({ message: 'Password is required.' })
  readonly password: string;

  @IsString({ message: 'Password confirmation must be a string.' })
  @IsNotEmpty({ message: 'Password confirmation is required.' })
  @Validate(MatchPasswordConstraint, {
    message: 'Password confirmation does not match the password.',
  })
  readonly passwordConfirmation: string;

  @IsString({ message: 'Name must be a string.' })
  @IsNotEmpty({ message: 'Name is required.' })
  readonly name: string;
}
