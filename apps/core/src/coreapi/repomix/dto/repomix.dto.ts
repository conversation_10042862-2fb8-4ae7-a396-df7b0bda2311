import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsPositive,
  IsString,
  ValidateNested,
} from 'class-validator';

enum OutputStyle {
  MARKDOWN = 'markdown',
  XML = 'xml',
  PLAIN = 'plain',
}

class OutputConfigDto {
  @IsOptional()
  @IsBoolean()
  showLineNumbers?: boolean;

  @IsOptional()
  @IsBoolean()
  removeComments?: boolean;

  @IsOptional()
  @IsBoolean()
  removeEmptyLines?: boolean;

  @IsOptional()
  @IsEnum(OutputStyle)
  style?: OutputStyle;

  @IsOptional()
  @IsBoolean()
  compress?: boolean;

  @IsOptional()
  @IsBoolean()
  fileSummary?: boolean;

  @IsOptional()
  @IsBoolean()
  directoryStructure?: boolean;
}

class ConfigDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => OutputConfigDto)
  output?: OutputConfigDto;
}

export class RepomixRequestDto {
  @IsObject()
  @IsNotEmpty()
  files: Record<string, string>;

  @IsOptional()
  @ValidateNested()
  @Type(() => ConfigDto)
  config?: ConfigDto;
}

class FileStatsDto {
  @IsNumber()
  @IsPositive()
  totalFiles: number;

  @IsObject()
  filesByExtension: Record<string, number>;
}

export class RepomixResponseDto {
  @IsString()
  @IsNotEmpty()
  content: string;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  totalFiles?: number;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  totalCharacters?: number;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  totalTokens?: number;

  @IsOptional()
  @IsObject()
  fileCharCounts?: Record<string, number>;

  @IsOptional()
  @IsObject()
  fileTokenCounts?: Record<string, number>;

  @IsOptional()
  @IsString()
  fileTree?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => FileStatsDto)
  fileStats?: FileStatsDto;
}
