import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import * as fs from 'fs/promises';
import * as os from 'os';
import * as path from 'path';
import { RepomixRequestDto, RepomixResponseDto } from './dto/repomix.dto';

@Injectable()
export class RepomixService implements OnModuleInit {
  private readonly logger = new Logger(RepomixService.name);
  private repomixModule: any;

  async onModuleInit() {
    try {
      // Use Function constructor to prevent TypeScript from transforming the dynamic import
      const dynamicImport = new Function(
        'specifier',
        'return import(specifier)',
      );
      this.repomixModule = await dynamicImport('repomix');
      this.logger.log('Repomix module loaded successfully');
    } catch (error) {
      this.logger.error('Failed to load repomix module:', error);
      throw error;
    }
  }

  // Helper function to generate file statistics
  private generateFileStats(files: Array<{ path: string; content: string }>) {
    const filesByExtension: Record<string, number> = {};

    files.forEach((file) => {
      const ext = file.path.split('.').pop() || 'no-extension';
      filesByExtension[ext] = (filesByExtension[ext] || 0) + 1;
    });

    return {
      totalFiles: files.length,
      filesByExtension,
    };
  }

  async generateRepoPack(
    request: RepomixRequestDto,
  ): Promise<RepomixResponseDto> {
    const startTime = Date.now();
    let tempDir: string | null = null;

    try {
      this.logger.log(`Processing ${Object.keys(request.files).length} files`);

      // Get repomix module (preloaded in onModuleInit)
      const { runCli } = this.repomixModule;

      // Create temporary directory
      tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'repomix-'));

      // Write files to temporary directory
      for (const [filePath, content] of Object.entries(request.files)) {
        const fullPath = path.join(tempDir, filePath);
        const dir = path.dirname(fullPath);

        // Ensure directory exists
        await fs.mkdir(dir, { recursive: true });

        // Write file
        await fs.writeFile(fullPath, content, 'utf8');
      }

      // Determine output style and file extension
      const outputStyle = request.config?.output?.style || 'xml';
      const outputExtension =
        outputStyle === 'xml'
          ? 'xml'
          : outputStyle === 'markdown'
            ? 'md'
            : 'txt';
      const outputFileName = `repomix-output.${outputExtension}`;

      // Prepare CLI options to match standard repomix behavior
      const cliOptions = {
        output: path.join(tempDir, outputFileName),
        style: outputStyle,
        compress: request.config?.output?.compress || false,
        quiet: true,
        removeComments: request.config?.output?.removeComments || false,
        removeEmptyLines: request.config?.output?.removeEmptyLines || false,
        showLineNumbers: request.config?.output?.showLineNumbers ?? false,
        fileSummary: request.config?.output?.fileSummary ?? true,
        directoryStructure: request.config?.output?.directoryStructure ?? true,
        // Ensure we get the standard repomix format
        parsableStyle: false, // This ensures proper formatting
      };

      // Run repomix CLI on the temporary directory
      const result = await runCli(['.'], tempDir, cliOptions);

      // Read the generated output file
      const outputPath = path.join(tempDir, outputFileName);
      const content = await fs.readFile(outputPath, 'utf8');

      // Extract pack result information
      const packResult = result.packResult;
      const fileStats = this.generateFileStats(
        Object.entries(request.files).map(([filePath, content]) => ({
          path: filePath,
          content,
        })),
      );

      const processingTime = Date.now() - startTime;
      this.logger.log(
        `Successfully generated repo pack with ${packResult.totalFiles} files, ${packResult.totalTokens} tokens in ${processingTime}ms`,
      );

      return {
        content,
        totalFiles: packResult.totalFiles,
        totalCharacters: packResult.totalCharacters,
        totalTokens: packResult.totalTokens,
        fileCharCounts: packResult.fileCharCounts,
        fileTokenCounts: packResult.fileTokenCounts,
        fileTree: '', // File tree is included in the content by default
        fileStats,
      };
    } catch (error) {
      this.logger.error(
        `Failed to generate repo pack: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to generate repo pack: ${error.message}`);
    } finally {
      // Clean up temporary directory
      if (tempDir) {
        try {
          await fs.rm(tempDir, { recursive: true, force: true });
        } catch (cleanupError) {
          this.logger.warn(
            `Failed to clean up temp directory: ${cleanupError.message}`,
          );
        }
      }
    }
  }
}
