import {
  Body,
  Controller,
  HttpException,
  HttpStatus,
  Logger,
  Post,
} from '@nestjs/common';
import { RepomixRequestDto, RepomixResponseDto } from './dto/repomix.dto';
import { RepomixService } from './repomix.service';

@Controller('api/repomix')
export class RepomixController {
  private readonly logger = new Logger(RepomixController.name);

  constructor(private readonly repomixService: RepomixService) {}

  private validateRequest(request: RepomixRequestDto): void {
    // Validate that files object is not empty
    if (!request.files || Object.keys(request.files).length === 0) {
      throw new HttpException(
        'Files object cannot be empty',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Validate file limits
    const fileCount = Object.keys(request.files).length;
    if (fileCount > 1000) {
      throw new HttpException(
        `Too many files. Maximum allowed: 1000, received: ${fileCount}`,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Validate file content and size
    let totalSize = 0;
    for (const [filePath, content] of Object.entries(request.files)) {
      if (typeof content !== 'string') {
        throw new HttpException(
          `File content for ${filePath} must be a string`,
          HttpStatus.BAD_REQUEST,
        );
      }

      // Check individual file size (1MB limit)
      const fileSize = Buffer.byteLength(content, 'utf8');
      if (fileSize > 1024 * 1024) {
        throw new HttpException(
          `File ${filePath} is too large. Maximum size: 1MB, actual: ${Math.round(fileSize / 1024)}KB`,
          HttpStatus.BAD_REQUEST,
        );
      }

      totalSize += fileSize;
    }

    // Check total payload size (10MB limit)
    if (totalSize > 10 * 1024 * 1024) {
      throw new HttpException(
        `Total payload too large. Maximum size: 10MB, actual: ${Math.round(totalSize / (1024 * 1024))}MB`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('generate')
  async generateRepoPack(
    @Body() request: RepomixRequestDto,
  ): Promise<RepomixResponseDto> {
    try {
      this.logger.log(
        `Received repomix request with ${Object.keys(request.files).length} files`,
      );

      this.validateRequest(request);

      const result = await this.repomixService.generateRepoPack(request);

      this.logger.log(`Successfully processed repomix request`);
      return result;
    } catch (error) {
      this.logger.error(
        `Error processing repomix request: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Internal server error while processing repomix request',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
