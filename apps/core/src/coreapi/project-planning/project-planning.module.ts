import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtGuardModule } from '@shared-library/guard/jwt.module';
import { SupabaseModule } from '@shared-library/modules/supabase/supabase.module';
import { PersistenceModule } from 'src/persistence/persistence.module';
import { ProjectPlanningController } from './project-planning.controller';
import { ProjectPlanningService } from './project-planning.service';

@Module({
  imports: [
    PersistenceModule,
    JwtGuardModule,
    SupabaseModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
  ],
  providers: [ProjectPlanningService],
  controllers: [ProjectPlanningController],
  exports: [ProjectPlanningService],
})
export class ProjectPlanningModule {}
