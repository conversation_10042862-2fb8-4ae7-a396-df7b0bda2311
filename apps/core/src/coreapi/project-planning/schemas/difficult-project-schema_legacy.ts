import { z } from 'zod';

// Core entity schema
const EntitySchema = z.object({
  id: z.string(),
  title: z.string(),
  status: z.enum(['planned', 'in_progress', 'completed', 'deprecated']),
  tags: z.array(z.string()).optional(),
});

// Dependency schema
const DependencySchema = z.object({
  prerequisites: z.array(z.string()).optional(),
  buildDependencies: z.array(z.string()).optional(),
});

// Code snippet schema
const CodeSnippetSchema = z.object({
  title: z.string(),
  language: z.enum([
    'ts',
    'js',
    'jsx',
    'tsx',
    'py',
    'html',
    'css',
    'json',
    'graphql',
    'sql',
    'sh',
  ]),
  code: z.string(),
  context: z.enum(['frontend', 'backend', 'testing', 'deployment']),
  description: z.string().optional(),
  scope: z.enum(['global', 'module', 'page', 'component']),
  filePath: z.string().optional(),
  isClientComponent: z.boolean(),
  isServerComponent: z.boolean().optional(),
});

// TanStack Query config schema
const TanStackQueryConfigSchema = z.object({
  queryKey: z.array(z.string()),
  queryFn: z.string(),
  options: z
    .object({
      staleTime: z.number().optional(),
      cacheTime: z.number().optional(),
      refetchOnWindowFocus: z.boolean().optional(),
      refetchOnMount: z.boolean().optional(),
      retry: z.union([z.number(), z.boolean()]).optional(),
      enabled: z.boolean().optional(),
      initialData: z.any().optional(),
    })
    .optional(),
  isInfiniteQuery: z.boolean().optional(),
  mutations: z
    .array(
      z.object({
        name: z.string(),
        mutationFn: z.string(),
      }),
    )
    .optional(),
});

// API endpoint schema
const APIEndpointSchema = z.object({
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
  path: z.string(),
  requestSchema: z.record(z.any()).optional(),
  responseSchema: z.record(z.any()).optional(),
  requiresAuth: z.boolean(),
  description: z.string().optional(),
  scope: z.enum(['global', 'module', 'page']),
  implementation: z.enum([
    'supabase',
    'nextjs-api',
    'nextjs-server-action',
    'external',
  ]),
  supabaseConfig: z
    .object({
      table: z.string().optional(),
      operation: z.enum(['select', 'insert', 'update', 'delete', 'rpc']),
      queryParams: z.record(z.any()).optional(),
      rpcFunction: z.string().optional(),
    })
    .optional(),
  tanstackQuery: TanStackQueryConfigSchema.optional(),
  nextjsConfig: z
    .object({
      routePath: z.string(),
      middleware: z.array(z.string()).optional(),
      isClientComponent: z.boolean(),
    })
    .optional(),
});

// Supabase table schema
const SupabaseTableSchema = z.object({
  name: z.string(),
  schema: z.union([
    z.literal('public'),
    z.literal('auth'),
    z.literal('storage'),
    z.string(),
  ]),
  columns: z.array(
    z.object({
      name: z.string(),
      type: z.string(),
      nullable: z.boolean(),
      isPrimaryKey: z.boolean(),
      hasDefault: z.boolean(),
      foreignKey: z
        .object({
          table: z.string(),
          column: z.string(),
        })
        .optional(),
    }),
  ),
  policies: z
    .array(
      z.object({
        name: z.string(),
        operation: z.enum(['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'ALL']),
        definition: z.string(),
      }),
    )
    .optional(),
});

// Implementation schema
const ImplementationSchema = z.object({
  description: z.string(),
  code: z.array(CodeSnippetSchema).optional(),
  apis: z.array(APIEndpointSchema).optional(),
  dataModels: z
    .array(
      z.object({
        name: z.string(),
        schema: z.record(z.any()),
        description: z.string().optional(),
        scope: z.enum(['global', 'module', 'page']),
        supabaseTable: SupabaseTableSchema.optional(),
      }),
    )
    .optional(),
  stateManagement: z
    .object({
      approach: z.enum(['zustand', 'useState']),
      keyStates: z.array(z.string()),
      scope: z.enum(['global', 'module', 'page', 'component']),
      implementation: z.string().optional(),
    })
    .optional(),
  queries: z.array(TanStackQueryConfigSchema).optional(),
  shadcnComponents: z
    .array(
      z.object({
        name: z.string(),
        props: z.record(z.any()).optional(),
        customization: z.string().optional(),
      }),
    )
    .optional(),
  forms: z
    .array(
      z.object({
        name: z.string(),
        fields: z.array(
          z.object({
            name: z.string(),
            type: z.string(),
            validation: z.record(z.any()).optional(),
            defaultValue: z.any().optional(),
            shadcnComponent: z.string().optional(),
          }),
        ),
        onSubmit: z.string(),
      }),
    )
    .optional(),
  tables: z
    .array(
      z.object({
        name: z.string(),
        columns: z.array(
          z.object({
            id: z.string(),
            header: z.string(),
            accessorKey: z.string().optional(),
            cell: z.string().optional(),
          }),
        ),
        features: z.array(z.string()),
        dataSource: z.string(),
      }),
    )
    .optional(),
});

// Story type enum
const StoryTypeSchema = z.enum([
  'epic',
  'feature',
  'component',
  'task',
  'sub_task',
]);

// Story schema
const StorySchema = EntitySchema.extend({
  ...DependencySchema.shape,
  type: StoryTypeSchema,
  parentId: z.string().optional(),
  complexity: z.number(),
  breakdownIds: z.array(z.string()).optional(),
  design: z
    .object({
      components: z.array(z.string()).optional(),
    })
    .optional(),
  implementation: ImplementationSchema,
  componentScope: z.enum(['global', 'module', 'page']).optional(),
  filePath: z.string().optional(),
  isClientComponent: z.boolean(),
});

// Next.js layout schema
const NextjsLayoutSchema = EntitySchema.extend({
  type: z.enum(['auth', 'dashboard', 'public', 'admin', 'custom']),
  implementation: ImplementationSchema,
  filePath: z.string(),
  isRootLayout: z.boolean(),
  metadata: z
    .object({
      title: z.string().optional(),
      description: z.string().optional(),
      additionalMetadata: z.record(z.any()).optional(),
    })
    .optional(),
  supportedSegments: z.array(z.string()),
  shadcnProviders: z
    .array(
      z.object({
        name: z.string(),
        config: z.record(z.any()).optional(),
      }),
    )
    .optional(),
  isClientComponent: z.boolean(),
});

// Page type enum
const PageTypeSchema = z.enum([
  'auth',
  'dashboard',
  'form',
  'list',
  'detail',
  'settings',
  'landing',
  'error',
]);

// Page schema
const PageSchema = EntitySchema.extend({
  ...DependencySchema.shape,
  type: PageTypeSchema,
  layoutId: z.string(),
  stories: z.array(StorySchema),
  route: z.string(),
  description: z.string(),
  filePath: z.string(),
  isClientComponent: z.boolean(),
  pageParams: z
    .array(
      z.object({
        name: z.string(),
        type: z.string(),
        isOptional: z.boolean(),
      }),
    )
    .optional(),
});

// Zustand store schema
const ZustandStoreSchema = EntitySchema.extend({
  scope: z.enum(['global', 'module', 'page']),
  schema: z.record(z.any()),
  initialState: z.record(z.any()),
  selectors: z
    .array(
      z.object({
        name: z.string(),
        implementation: z.string(),
      }),
    )
    .optional(),
  actions: z.array(
    z.object({
      name: z.string(),
      implementation: z.string(),
    }),
  ),
  persistence: z
    .object({
      type: z.enum(['localStorage', 'sessionStorage', 'none']),
      key: z.string().optional(),
    })
    .optional(),
  filePath: z.string(),
  isAuthStore: z.boolean().optional(),
});

// Shared component schema
const SharedComponentSchema = EntitySchema.extend({
  scope: z.enum(['global', 'module']),
  implementation: ImplementationSchema,
  shadcn: z
    .object({
      isCustomized: z.boolean(),
      baseName: z.string().optional(),
      requiredComponents: z.array(z.string()).optional(),
    })
    .optional(),
  filePath: z.string(),
  isClientComponent: z.boolean(),
  props: z.array(
    z.object({
      name: z.string(),
      type: z.string(),
      required: z.boolean(),
      defaultValue: z.any().optional(),
    }),
  ),
});

// Query config schema
const QueryConfigSchema = EntitySchema.extend({
  scope: z.enum(['global', 'module']),
  filePath: z.string(),
  queries: z.array(TanStackQueryConfigSchema),
  prefetchingStrategy: z
    .object({
      type: z.enum(['static', 'dynamic']),
      implementation: z.string().optional(),
    })
    .optional(),
  axiosConfig: z
    .object({
      baseURL: z.string().optional(),
      timeout: z.number().optional(),
      headers: z.record(z.any()).optional(),
      interceptors: z
        .object({
          request: z.string().optional(),
          response: z.string().optional(),
        })
        .optional(),
    })
    .optional(),
});

// Module schema
const ModuleSchema = EntitySchema.extend({
  ...DependencySchema.shape,
  pages: z.array(PageSchema),
  description: z.string(),
  layouts: z.array(NextjsLayoutSchema).optional(),
  zustandStores: z.array(ZustandStoreSchema).optional(),
  sharedComponents: z.array(SharedComponentSchema).optional(),
  routeSegment: z.string().optional(),
});

// Product schema
const ProductSchema = EntitySchema.extend({
  modules: z.array(ModuleSchema),
  description: z.string(),
  globalLayouts: z.array(NextjsLayoutSchema),
  globalZustandStores: z.array(ZustandStoreSchema),
  globalComponents: z.array(SharedComponentSchema),
  globalQueryConfigs: z.array(QueryConfigSchema),
  sqlSchemaForSupabase: z.string().optional(),
});

export {
  APIEndpointSchema,
  CodeSnippetSchema,
  DependencySchema,
  EntitySchema,
  ImplementationSchema,
  ModuleSchema,
  NextjsLayoutSchema,
  PageSchema,
  PageTypeSchema,
  ProductSchema,
  QueryConfigSchema,
  SharedComponentSchema,
  StorySchema,
  StoryTypeSchema,
  SupabaseTableSchema,
  TanStackQueryConfigSchema,
  ZustandStoreSchema,
};
