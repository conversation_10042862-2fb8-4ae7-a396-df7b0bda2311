import { z } from 'zod';
import { LayoutSchema, PageSchema, StorySchema } from './core-schema';

export const ModuleDetailSchema = z.object({
  moduleId: z.string(),

  // Full Next.js App Router implementations using simplified schemas
  pages: z.array(PageSchema), // page.tsx files
  layouts: z.array(LayoutSchema).optional(), // layout.tsx files
  stories: z.array(StorySchema), // User stories with implementation

  // ADDED: SQL for the module
  completeSupabaseSQL: z
    .string()
    .describe(
      'Complete SQL DDL and DML for creating all Supabase tables, RLS policies, and initial data for this module.',
    ),
});

export type ModuleDetail = z.infer<typeof ModuleDetailSchema>;
