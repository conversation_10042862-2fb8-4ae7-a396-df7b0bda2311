import { z } from 'zod';

// Core entity schema
const EntitySchema = z.object({
  id: z.string(),
  title: z.string(),
  status: z.string(),
  description: z.string().optional(),
});

// Simplified code snippet schema
const CodeSnippetSchema = z.object({
  title: z.string(),
  language: z.string(),
  code: z.string().max(650), // Assuming a max length for code snippets
  filePath: z.string().optional(),
});

// Direct Supabase operation schema
const SupabaseOperationSchema = z.object({
  table: z.string(),
  operation: z.string(), // "select", "insert", "update", "delete", "rpc", "bucket_upload", "bucket_download"
  description: z.string().optional(),
  requiresAuth: z.boolean().optional(),
  filters: z.string().optional(), // JSON or description of filters
  returnedFields: z.array(z.string()).optional(),
  bucket: z.string().optional(), // For storage operations
  functionName: z.string().optional(), // For RPC operations
});

// Custom API endpoint schema
const APIEndpointSchema = z.object({
  method: z.string(),
  path: z.string(),
  description: z.string().optional(),
  requiresAuth: z.boolean().optional(),
  implementationType: z.string(), // "nextjs-api", "server-action", "edge-function"
  usesSupabase: z.boolean().optional(),
  supabaseDetails: z.string().optional(), // Description of how Supabase is used
});

// Simplified data model schema
const DataModelSchema = z.object({
  name: z.string(),
  fields: z.array(
    z.object({
      name: z.string(),
      fieldType: z.string(),
      required: z.boolean().optional(),
    }),
  ),
  supabaseTable: z.string().optional(),
});

// Simplified form schema
const FormSchema = z.object({
  name: z.string(),
  fields: z.array(
    z.object({
      name: z.string(),
      fieldType: z.string(),
      required: z.boolean().optional(),
    }),
  ),
  onSubmitOperation: z.string().optional(),
});

// Simplified story schema
const StorySchema = EntitySchema.extend({
  storyType: z.string(),
  parentId: z.string().optional(), // This can be a pageId or another storyId
  pageId: z.string().optional(), // Explicitly for linking to a page
  complexity: z.number(),
  implementation: z.object({
    description: z.string(),
    codeSnippets: z.array(CodeSnippetSchema).optional(),
    apiEndpoints: z.array(APIEndpointSchema).optional(),
    supabaseOperations: z.array(SupabaseOperationSchema).optional(),
    dataModels: z.array(DataModelSchema).optional(),
    forms: z.array(FormSchema).optional(),
    stateManagement: z.string().optional(),
    uiComponents: z
      .array(
        z.object({
          name: z.string(),
          type: z.string().optional(),
        }),
      )
      .optional(),
  }),
  isClientComponent: z.boolean().optional(),
  requiredSupabaseTables: z.array(z.string()).optional(),
});

// Simplified page schema
const PageSchema = EntitySchema.extend({
  pageType: z.string(),
  layoutId: z.string(),
  storyIds: z.array(z.string()),
  route: z.string(),
  filePath: z.string().optional(),
  isClientComponent: z.boolean().optional(),
});

// Simplified layout schema
const LayoutSchema = EntitySchema.extend({
  layoutType: z.string(),
  filePath: z.string().optional(),
  isRootLayout: z.boolean().optional(),
  isClientComponent: z.boolean().optional(),
});

// Simplified component schema
const ComponentSchema = EntitySchema.extend({
  scope: z.string(),
  implementation: z.object({
    description: z.string(),
    codeSnippets: z.array(CodeSnippetSchema).optional(),
  }),
  filePath: z.string().optional(),
  isClientComponent: z.boolean().optional(),
});

// Simplified module schema
const ModuleSchema = EntitySchema.extend({
  pages: z.array(PageSchema),
  layouts: z.array(LayoutSchema).optional(),
  components: z.array(ComponentSchema).optional(),
  routeSegment: z.string().optional(),
});

// Simplified product schema
const ProductSchema = EntitySchema.extend({
  modules: z.array(ModuleSchema),
  globalLayouts: z.array(LayoutSchema).optional(),
  globalComponents: z.array(ComponentSchema).optional(),
  stories: z.array(StorySchema),
  completeSupabaseSQL: z.string(),
});

export {
  APIEndpointSchema,
  CodeSnippetSchema,
  ComponentSchema,
  DataModelSchema,
  EntitySchema,
  FormSchema,
  LayoutSchema,
  ModuleSchema,
  PageSchema,
  ProductSchema,
  StorySchema,
  SupabaseOperationSchema,
};
