import { z } from 'zod';

// Core entity schema
const EntitySchema = z.object({
  id: z.string(),
  title: z.string(),
  status: z.string(),
  description: z.string().optional(),
});

// Simplified Supabase operation schema - only essential fields
const SupabaseOperationSchema = z.object({
  table: z.string(),
  operation: z.enum(['select', 'insert', 'update', 'delete', 'upsert']),
  description: z.string(),
  // Optional: Add common modifiers
  orderBy: z.string().optional(), // e.g., "created_at DESC"
  filters: z.string().optional(), // e.g., "status = 'active'"
});

// Server Action schema - ONLY for sensitive operations that require server-side logic
// Use cases: admin operations, user management, complex business logic, service role privileges
const ServerActionSchema = z.object({
  functionName: z.string(), // Name of the server action function
  filePath: z.string(), // Path to the actions file (e.g., "app/actions/admin.ts")
  description: z.string(),
  requiresAdmin: z.boolean().optional(), // Whether it needs service role privileges
});

// Simplified form schema - description instead of detailed fields
const FormSchema = z.object({
  name: z.string(),
  description: z.string(), // High-level description instead of detailed fields array
  submitAction: z.string().optional(), // What happens on form submission
});

// Simplified story schema
// Massively simplified StorySchema
const StorySchema = EntitySchema.extend({
  storyType: z.string(),
  parentId: z.string().optional(), // This can be a pageId or another storyId
  pageId: z.string(), // Explicitly for linking to a page
  complexity: z.number(),
  // SIMPLIFIED implementation - just a detailed description for AI
  implementation: z.object({
    description: z.string(), // Detailed description for AI to work from
    // stateManagement: z.string().optional(), // Optional: if truly essential for planning
  }),
  isClientComponent: z.boolean().optional(),

  // OPERATION STRATEGY:
  // DEFAULT: Use 'direct' for all regular CRUD operations (create, read, update, delete)
  // ONLY use 'serverAction' for sensitive operations like:
  // - Admin user management, role assignments
  // - Complex business logic requiring server validation
  // - Operations needing service role privileges
  // - Multi-table transactions requiring consistency
  operationType: z.enum(['direct', 'serverAction']).default('direct'), // Default to direct client-side operations

  // For sensitive operations ONLY - use sparingly
  serverAction: ServerActionSchema.optional(),

  // For regular CRUD operations - use this for most cases
  supabaseOperation: SupabaseOperationSchema.optional(),
});

// Simplified page schema
const PageSchema = EntitySchema.extend({
  pageType: z.string(),
  layoutId: z.string(), // ID of the layout this page uses
  // storyIds: z.array(z.string()), // REMOVED: Stories will link to pages via pageId
  route: z.string(),
  // filePath: z.string().optional(), // REMOVED: AI can determine file path
  isClientComponent: z.boolean().optional(),
});

// Simplified layout schema
const LayoutSchema = EntitySchema.extend({
  layoutType: z.string(),
  // filePath: z.string().optional(), // REMOVED: AI can determine file path
  isRootLayout: z.boolean().optional(),
  isClientComponent: z.boolean().optional(),
});

// Simplified component schema
const ComponentSchema = EntitySchema.extend({
  scope: z.string(), // e.g., "shared", "module-specific"
  // REMOVED implementation object and filePath - AI will generate based on description
  isClientComponent: z.boolean().optional(),
});

// Simplified module schema - uses the simplified PageSchema, LayoutSchema, ComponentSchema
const ModuleSchema = EntitySchema.extend({
  pages: z.array(PageSchema),
  layouts: z.array(LayoutSchema).optional(),
  components: z.array(ComponentSchema).optional(),
  routeSegment: z.string().optional(),
});

// Simplified product schema - uses simplified sub-schemas
const ProductSchema = EntitySchema.extend({
  modules: z.array(ModuleSchema),
  globalLayouts: z.array(LayoutSchema).optional(),
  globalComponents: z.array(ComponentSchema).optional(),
  stories: z.array(StorySchema),
  completeSupabaseSQL: z.string(), // This was already here, relevant for overall product SQL
});

export {
  // CodeSnippetSchema, // REMOVED
  // APIEndpointSchema, // REPLACED with ServerActionSchema
  ComponentSchema,
  EntitySchema,
  FormSchema,
  LayoutSchema,
  ModuleSchema,
  PageSchema,
  ProductSchema,
  ServerActionSchema,
  StorySchema,
  SupabaseOperationSchema,
};
