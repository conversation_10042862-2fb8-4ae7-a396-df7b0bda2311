import { z } from 'zod';
// No longer importing DataModelSchema and APIEndpointSchema from simple-project-schema
// as they will be simplified to strings here.

export const ProjectBlueprintSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),

  // Application modules only (Next.js + Supabase context)
  modules: z.array(
    z.object({
      id: z.string(),
      title: z.string(),
      description: z.string(),
      routeSegment: z.string(), // "/auth", "/dashboard", "/products"
      // REMOVED: pageCount, componentCount, storyCount, complexity
      pages: z.array(
        // NEW: Basic pages info for UI
        z.object({
          id: z.string(),
          title: z.string(),
          description: z
            .string()
            .describe(
              'Describe comprehensive page functionality. For simple apps, combine multiple operations (list, create, edit, delete) into single page descriptions.',
            ),
          route: z.string(), // e.g., "/profile", "/settings"
          coreDataModels: z
            .array(
              z
                .string()
                .describe(
                  "High-level description of a core data model, e.g., 'User (id, name, email), Product (id, name, price)'",
                ),
            )
            .optional(),
          coreApiEndpoints: z
            .array(
              z
                .string()
                .describe(
                  "High-level description of a core API endpoint, e.g., 'POST /api/login for user authentication'",
                ),
            )
            .optional(),
        }),
      ),
      dependencies: z.array(z.string()).optional(), // Other module IDs
    }),
  ),

  // Authentication requirements (Kept as-is)
  authRequirements: z.object({
    hasAuth: z.boolean(),
    // authTypes: z.array(z.string()), // ["email", "google", "github"]
    authTypes: z.enum(['email']),
    protectedRoutes: z.array(z.string()),
  }),
});

export type ProjectBlueprint = z.infer<typeof ProjectBlueprintSchema>;
