import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Res,
  UnprocessableEntityException,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileService } from '@shared-library/modules/file/file.service';
import { Response } from 'express';
import { promises as fs } from 'fs';
import { join } from 'path';

@Controller({
  path: 'files',
})
export class FileController {
  constructor(private readonly fileService: FileService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file', FileService.multerOptions()))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    if (!file) throw new UnprocessableEntityException('file required');
    const storageType = process.env.STORAGE_TYPE || 'local';

    if (storageType !== 'local') {
      throw new HttpException(
        `Storage type '${storageType}' is not supported for this operation.`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const filePath = await this.fileService.uploadFile(file);
    return { message: 'File uploaded successfully', path: filePath };
  }

  @Post('delete')
  async deleteFile(@Body('fileName') fileName: string) {
    await this.fileService.removeFile(fileName);
    return { message: 'File deleted successfully' };
  }

  @Get(':fileName')
  async getFile(@Param('fileName') fileName: string, @Res() res: Response) {
    const filePath = join(process.cwd(), 'file-storage/private', fileName);

    try {
      await fs.access(filePath); // Check if the file exists
      res.sendFile(filePath); // Serve the file
    } catch (error) {
      throw new HttpException('File not found.', HttpStatus.NOT_FOUND);
    }
  }
}
