import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/persistence/prisma/prisma.service';

@Injectable()
export class OnboardingService {
  constructor(private readonly prisma: PrismaService) {}

  async updateProgress(workspaceId: string, newData: Record<string, any>) {
    const existing = await this.prisma.onboardingProgress.findUnique({
      where: { workspaceId: workspaceId },
    });

    if (existing) {
      const merged = { ...(existing.data as Record<string, any>), ...newData };

      return this.prisma.onboardingProgress.update({
        where: { workspaceId: workspaceId },
        data: { data: merged },
      });
    }

    return this.prisma.onboardingProgress.create({
      data: {
        workspaceId: workspaceId,
        data: newData,
      },
    });
  }

  async getProgress(workspaceId: string) {
    return this.prisma.onboardingProgress.findUnique({
      where: { workspaceId: workspaceId },
    });
  }
}
