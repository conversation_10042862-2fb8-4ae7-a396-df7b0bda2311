import { Body, Controller, Get, Param, Patch, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@shared-library/guard/jwt.guard';
import { OnboardingService } from './onboarding.service';

@Controller('onboarding')
@UseGuards(JwtAuthGuard)
export class OnboardingController {
  constructor(private readonly onboardingService: OnboardingService) {}

  @Patch(':workspaceId')
  updateProgress(
    @Param('workspaceId') workspaceId: string,
    @Body() body: Record<string, any>,
  ) {
    return this.onboardingService.updateProgress(workspaceId, body);
  }

  @Get(':workspaceId')
  getProgress(@Param('workspaceId') workspaceId: string) {
    return this.onboardingService.getProgress(workspaceId);
  }
}
