import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '@shared-library/guard/jwt.guard';
import { ApiRequest } from '@shared-library/modules/auth/interfaces/jwt-payload.interface';
import { WorkspacesService } from '@shared-library/modules/workspaces/workspaces.service';
import { AcceptInvitationDto } from './dto/accept-invitation.dto';
import { CreateDTO } from './dto/create.dto';
import { InviteMemberDto } from './dto/invite-member.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';

@Controller({
  path: 'workspace',
})
export class WorkspaceController {
  constructor(private readonly workspacesService: WorkspacesService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  getAllWorkspaces(@Req() request: ApiRequest) {
    return this.workspacesService.listAllWorkspaces(request.user.userId);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(JwtAuthGuard)
  create(@Req() request: ApiRequest, @Body() body: CreateDTO) {
    return this.workspacesService.create({
      name: body.name,
      ownerId: request.user.userId,
    });
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  get(@Param('id') id: string) {
    return this.workspacesService.get(id);
  }

  @Get(':id/members')
  @UseGuards(JwtAuthGuard)
  getMembers(@Req() request: ApiRequest, @Param('id') id: string) {
    return this.workspacesService.getMembers(id, request.user.userId);
  }

  @Post(':id/members')
  @UseGuards(JwtAuthGuard)
  inviteMember(
    @Req() request: ApiRequest,
    @Param('id') id: string,
    @Body() body: InviteMemberDto,
  ) {
    return this.workspacesService.inviteMember({
      workspaceId: id,
      ownerId: request.user.userId,
      role: body.role,
      email: body.email,
    });
  }

  @Post('invitations')
  @UseGuards(JwtAuthGuard)
  acceptMembershipInvitation(
    @Req() request: ApiRequest,
    @Body() body: AcceptInvitationDto,
  ) {
    return this.workspacesService.acceptMemberInvitation({
      code: body.code,
      userId: request.user.userId,
    });
  }

  @Delete(':id/members/:memberId')
  @UseGuards(JwtAuthGuard)
  removeMember(
    @Req() request: ApiRequest,
    @Param('id') id: string,
    @Param('memberId') memberId: string,
  ) {
    return this.workspacesService.removeMember(
      id,
      request.user.userId,
      memberId,
    );
  }

  @Get(':id/settings')
  @UseGuards(JwtAuthGuard)
  getSettings(@Req() request: ApiRequest, @Param('id') id: string) {
    return this.workspacesService.getSettings(id, request.user.userId);
  }

  @Put(':id/settings')
  @UseGuards(JwtAuthGuard)
  updateSettings(
    @Req() request: ApiRequest,
    @Param('id') id: string,
    @Body() body: UpdateSettingDto,
  ) {
    return this.workspacesService.updateSetting({
      workspaceId: id,
      userId: request.user.userId,
      key: body.key,
      value: body.value,
    });
  }
}
