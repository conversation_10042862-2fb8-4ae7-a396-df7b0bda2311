import { Modu<PERSON> } from '@nestjs/common';
import { JwtGuardModule } from '@shared-library/guard/jwt.module';
import { RefreshTokenGuardModule } from '@shared-library/guard/refresh-token.module';
import { TwoFactorGuardModule } from '@shared-library/guard/two-factor-guard.module';
import { AuthModule } from '@shared-library/modules/auth/auth.module';
import { UsersModule } from '@shared-library/modules/users/users.module';
import { WorkspacesModule } from '@shared-library/modules/workspaces/workspaces.module';
import { WorkspaceController } from './workspace.controller';

@Module({
  imports: [
    AuthModule,
    JwtGuardModule,
    RefreshTokenGuardModule,
    TwoFactorGuardModule,
    UsersModule,
    WorkspacesModule,
  ],
  providers: [],
  controllers: [WorkspaceController],
})
export class CoreWorkspaceModule {}
