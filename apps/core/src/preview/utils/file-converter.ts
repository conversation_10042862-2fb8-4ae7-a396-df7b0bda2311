export function convertFileTreeToFiles(fileTree: any): Record<string, string> {
  const files: Record<string, string> = {};

  function processNode(node: any, currentPath: string = '') {
    if (node.type === 'file') {
      files[currentPath] = node.content || '';
    } else if (node.type === 'directory' && node.children) {
      Object.entries(node.children).forEach(([name, child]) => {
        const newPath = currentPath ? `${currentPath}/${name}` : name;
        processNode(child, newPath);
      });
    }
  }

  processNode(fileTree);
  return files;
}

// Alternative converter for FileSystemTree format (used by frontend)
export function convertFileSystemTreeToFiles(
  fileTree: any,
): Record<string, string> {
  const files: Record<string, string> = {};

  function processNode(node: any, currentPath: string = '') {
    if (node.file) {
      // This is a file node
      files[currentPath] = node.file.contents || '';
    } else if (node.directory) {
      // This is a directory node
      Object.entries(node.directory).forEach(([name, child]) => {
        const newPath = currentPath ? `${currentPath}/${name}` : name;
        processNode(child, newPath);
      });
    }
  }

  // Process the root level
  Object.entries(fileTree).forEach(([name, node]) => {
    processNode(node, name);
  });

  return files;
}
