export interface KoyebCreateAppResponse {
  id: string;
  name: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
  started_at: null;
  succeeded_at: null;
  paused_at: null;
  resumed_at: null;
  terminated_at: null;
  status: string;
  messages: string[];
  version: string;
  domains: Domain[];
}

interface Domain {
  id: string;
  organization_id: string;
  name: string;
  created_at: string;
  updated_at: string;
  status: string;
  type: string;
  app_id: string;
  deployment_group: string;
  verified_at: null;
  intended_cname: string;
  messages: any[];
  version: string;
  cloudflare: Cloudflare;
}

interface Cloudflare {}
