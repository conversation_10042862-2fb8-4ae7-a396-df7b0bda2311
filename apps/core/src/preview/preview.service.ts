import { Injectable, Logger } from '@nestjs/common';
import { KoyebPreviewManager } from './koyeb-preview-manager';
import { convertFileSystemTreeToFiles } from './utils/file-converter';

@Injectable()
export class PreviewService {
  private readonly logger = new Logger(PreviewService.name);
  private koyebManager: KoyebPreviewManager;

  constructor() {
    const apiKey = process.env.KOYEB_API_KEY;
    const dockerImage = process.env.DOCKER_IMAGE;

    if (!apiKey) {
      throw new Error('KOYEB_API_KEY environment variable is required');
    }

    if (!dockerImage) {
      throw new Error('DOCKER_IMAGE environment variable is required');
    }

    this.koyebManager = new KoyebPreviewManager(apiKey, dockerImage);
    this.logger.log('PreviewService initialized with Koyeb integration');
  }

  async createPreview(fileTree: any) {
    this.logger.log('Starting preview creation process');

    try {
      // Step 1: Create Koyeb app with web service (2-3 seconds)
      this.logger.log('Creating Koyeb app with web service...');

      const createResult = await this.koyebManager.createPreviewApp(fileTree);

      const { serviceId, appId, appUrl } = createResult;

      // Step 2: Convert file tree to files map
      this.logger.log('Converting file tree to files map...');
      const files = convertFileSystemTreeToFiles(fileTree);
      this.logger.log(
        `Converted ${Object.keys(files).length} files for injection`,
      );

      // Step 3: Inject code into running container (1 second)
      this.logger.log('Injecting code into container...');
      await this.koyebManager.injectCode(appUrl, files);
      this.logger.log('Code injection completed successfully');

      const result = {
        serviceId,
        appId,
        previewUrl: appUrl,
        wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
        filesCount: Object.keys(files).length,
      };

      this.logger.log(`Preview created successfully: ${result.previewUrl}`);
      return result;
    } catch (error) {
      this.logger.error('Failed to create preview:', error);
      throw new Error(`Preview creation failed: ${error.message}`);
    }
  }

  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
    };
  }
}
