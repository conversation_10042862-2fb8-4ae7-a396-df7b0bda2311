import { Controller, Get, Param, Post } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AppService } from './app.service';
import { FileStorageService } from './markdown-processor/file-storage.service';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly eventEmitter: EventEmitter2,
    private readonly fileStorage: FileStorageService,
  ) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  // this is dummy endpoint only to check the markdown is working as expected
  @Post(':workspaceId/process-markdown')
  async processMarkdown(@Param('workspaceId') workspaceId: string) {
    const markdown = `
    File: \`src/components/UserProfile.tsx\`:
    \`\`\`typescript
    import React from 'react';
    import { Card, Avatar } from '@/components/ui';
    
    interface UserProfileProps {
      name: string;
      email: string;
      avatar: string;
    }
    
    export const UserProfile: React.FC<UserProfileProps> = ({
      name,
      email,
      avatar
    }) => {
      return (
        <Card className="p-4">
          <Avatar src={avatar} alt={name} />
          <h2>{name}</h2>
          <p>{email}</p>
        </Card>
      );
    };
    \`\`\`
    
    File: \`src/services/api.ts\`:
    \`\`\`typescript
    import axios from 'axios';
    
    const api = axios.create({
      baseURL: process.env.API_URL
    });
    
    export const getUserProfile = async (userId: string) => {
      const response = await api.get(\`/users/\${userId}\`);
      return response.data;
    };
    \`\`\`
    
    File: \`src/styles/profile.css\`:
    \`\`\`css
    .profile-card {
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 1rem;
    }
    
    .profile-avatar {
      width: 64px;
      height: 64px;
      border-radius: 50%;
    }
    \`\`\`
    
    File: \`README.md\`:
    \`\`\`markdown
    # User Profile Component
    
    This component displays user profile information in a card format.
    
    ## Usage
    
    \`\`\`jsx
    <UserProfile
      name="John Doe"
      email="<EMAIL>"
      avatar="/avatars/john.jpg"
    />
    \`\`\`
    \`\`\`
    `;

    const metadata = {
      workspaceId,
      version: '1',
    };
    this.eventEmitter.emit('markdown.created', {
      workspaceId,
      markdown: markdown,
      metadata: metadata,
    });

    return { message: 'Markdown processing initiated', workspaceId };
  }

  @Get(':workspaceId/files')
  async getWorkspaceFiles(@Param('workspaceId') workspaceId: string) {
    const files = await this.fileStorage.getWorkspaceFiles(workspaceId);
    return { workspaceId, files };
  }

  @Get(':workspaceId/files/:filePath')
  async getFile(
    @Param('workspaceId') workspaceId: string,
    @Param('filePath') filePath: string,
  ) {
    const file = await this.fileStorage.getFile(workspaceId, filePath);
    return file;
  }
}
