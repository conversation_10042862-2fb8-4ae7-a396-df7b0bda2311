import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Queue } from 'bull';
import { FileStorageService } from './file-storage.service';
import { ProcessedFile } from './types/markdown-processor.types';

@Injectable()
export class MarkdownProcessorService {
  private readonly logger = new Logger(MarkdownProcessorService.name);

  constructor(
    @InjectQueue('markdown-processing') private readonly queue: Queue,
    private readonly fileStorage: FileStorageService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async processMarkdown(
    workspaceId: string,
    markdown: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    this.logger.log(`Processing markdown for workspace: ${workspaceId}`);
    console.log('catched markdown event successfully');

    const files = await this.parseMarkdownFiles(workspaceId, markdown);

    for (const file of files) {
      console.log('adding file to queue');
      await this.queue.add(
        'process-file',
        {
          workspaceId,
          file,
          metadata,
        },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      );
    }
  }

  private async parseMarkdownFiles(
    workspaceId: any,
    markdown: string,
  ): Promise<ProcessedFile[]> {
    const fileRegex = /File:\s*`([^`]+)`:\s*```(\w+)\n([\s\S]*?)```/g;
    const files: ProcessedFile[] = [];
    let match;
    console.log('now parsing the markdown files');
    while ((match = fileRegex.exec(markdown)) !== null) {
      const [_, filePath, language, content] = match;
      files.push({
        workspaceId,
        filePath: filePath.trim(),
        content: content.trim(),
        language,
      });
    }
    console.log('parsed the markdown files');
    return files;
  }
}
