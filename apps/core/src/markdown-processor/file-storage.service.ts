import { InjectRedis } from '@nestjs-modules/ioredis';
import { Injectable, Logger } from '@nestjs/common';
import Redis from 'ioredis';
import { ProcessedFile } from './types/markdown-processor.types';

@Injectable()
export class FileStorageService {
  private readonly logger = new Logger(FileStorageService.name);

  constructor(@InjectRedis() private readonly redis: Redis) {}

  private getFileKey(workspaceId: string, filePath: string): string {
    return `workspace:${workspaceId}:file:${filePath}`;
  }

  private getWorkspaceKey(workspaceId: string): string {
    return `workspace:${workspaceId}:files`;
  }

  async storeFile(file: ProcessedFile): Promise<void> {
    const fileKey = this.getFileKey(file.workspaceId, file.filePath);
    const workspaceKey = this.getWorkspaceKey(file.workspaceId);

    try {
      const result = await this.redis
        .multi()
        .hset(
          fileKey,
          'content',
          file.content,
          'language',
          file.language,
          'metadata',
          JSON.stringify(file.metadata || {}),
          'updatedAt',
          new Date().toISOString(),
        )
        .sadd(workspaceKey, file.filePath)
        .exec();

      if (!result) {
        throw new Error('Redis transaction failed');
      }

      this.logger.debug(
        `Stored file ${file.filePath} for workspace ${file.workspaceId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to store file ${file.filePath} for workspace ${file.workspaceId}`,
        error,
      );
      throw error;
    }
  }

  async getFile(
    workspaceId: string,
    filePath: string,
  ): Promise<ProcessedFile | null> {
    try {
      const fileKey = this.getFileKey(workspaceId, filePath);
      const fileData = await this.redis.hgetall(fileKey);

      if (!fileData || !fileData.content) {
        return null;
      }

      return {
        workspaceId,
        filePath,
        content: fileData.content,
        language: fileData.language,
        metadata: JSON.parse(fileData.metadata || '{}'),
      };
    } catch (error) {
      this.logger.error(
        `Failed to get file ${filePath} for workspace ${workspaceId}`,
        error,
      );
      throw error;
    }
  }

  async getWorkspaceFiles(workspaceId: string): Promise<string[]> {
    try {
      const workspaceKey = this.getWorkspaceKey(workspaceId);
      return await this.redis.smembers(workspaceKey);
    } catch (error) {
      this.logger.error(
        `Failed to get files for workspace ${workspaceId}`,
        error,
      );
      throw error;
    }
  }

  async deleteWorkspaceFile(
    workspaceId: string,
    filePath: string,
  ): Promise<boolean> {
    const fileKey = this.getFileKey(workspaceId, filePath);
    const workspaceKey = this.getWorkspaceKey(workspaceId);

    try {
      const result = await this.redis
        .multi()
        .del(fileKey)
        .srem(workspaceKey, filePath)
        .exec();

      return result !== null;
    } catch (error) {
      this.logger.error(
        `Failed to delete file ${filePath} from workspace ${workspaceId}`,
        error,
      );
      throw error;
    }
  }
}
