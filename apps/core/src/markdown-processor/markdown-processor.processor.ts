import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Job } from 'bull';
import { FileStorageService } from './file-storage.service';
import { ProcessedFile } from './types/markdown-processor.types';

@Injectable()
@Processor('markdown-processing')
export class MarkdownProcessor {
  private readonly logger = new Logger(MarkdownProcessor.name);

  constructor(
    private readonly fileStorage: FileStorageService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  @Process('process-file')
  async processFile(
    job: Job<{ workspaceId: string; file: ProcessedFile; metadata: any }>,
  ) {
    const { workspaceId, file, metadata } = job.data;
    this.logger.log(
      `Processing file ${file.filePath} for workspace ${workspaceId}`,
    );

    try {
      await this.fileStorage.storeFile({
        ...file,
        workspaceId,
        metadata,
      });

      this.eventEmitter.emit('file.processed', {
        workspaceId,
        filePath: file.filePath,
        language: file.language,
        metadata,
      });

      return { success: true, filePath: file.filePath };
    } catch (error) {
      this.logger.error(`Error processing file ${file.filePath}:`, error);
      throw error;
    }
  }
}
