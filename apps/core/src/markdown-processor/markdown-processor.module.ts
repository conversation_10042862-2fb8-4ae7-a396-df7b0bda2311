import { RedisModule } from '@nestjs-modules/ioredis';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { FileStorageService } from './file-storage.service';
import { MarkdownProcessorListener } from './markdown-processor.listener';
import { MarkdownProcessor } from './markdown-processor.processor';
import { MarkdownProcessorService } from './markdown-processor.service';

@Module({
  imports: [
    RedisModule.forRootAsync({
      useFactory: () => ({
        type: 'single',
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
      }),
    }),
    BullModule.registerQueue({
      name: 'markdown-processing',
    }),
    EventEmitterModule.forRoot(),
  ],
  providers: [
    MarkdownProcessorService,
    MarkdownProcessorListener,
    FileStorageService,
    MarkdownProcessor,
  ],
  exports: [MarkdownProcessorService, FileStorageService],
})
export class MarkdownProcessorModule {}
