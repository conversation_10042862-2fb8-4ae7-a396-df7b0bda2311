import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { MarkdownProcessorService } from './markdown-processor.service';
import { MarkdownCreatedEvent } from './types/markdown-processor.types';

@Injectable()
export class MarkdownProcessorListener {
  constructor(private readonly processorService: MarkdownProcessorService) {}

  @OnEvent('markdown.created')
  async handleMarkdownCreated(event: MarkdownCreatedEvent) {
    console.log('listening to the markdown created event');
    await this.processorService.processMarkdown(
      event.workspaceId,
      event.markdown,
      event.metadata,
    );
  }
}
