import { randomBytes } from 'crypto';

export const generateCryptoSecret = (length: number): string => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const charactersLength = characters.length;
  const randomBytesLength = Math.ceil((length * 3) / 4);

  let secret = '';
  while (secret.length < length) {
    const randomBytesBuffer = randomBytes(randomBytesLength);
    for (
      let i = 0;
      i < randomBytesBuffer.length && secret.length < length;
      i++
    ) {
      const randomIndex = randomBytesBuffer.readUInt8(i) % charactersLength;
      secret += characters.charAt(randomIndex);
    }
  }
  return secret;
};

