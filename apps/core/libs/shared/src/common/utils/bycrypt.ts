import * as bcrypt from 'bcrypt';

export const hashPassword = async (password: string): Promise<string> => {
  const hashedPassword = await bcrypt.hash(password, 10);
  return hashedPassword;
};

export const comparePassword = async (
  password: string,
  hashedPassword: string,
): Promise<boolean> => {
  const isPasswordMatch = await bcrypt.compare(password, hashedPassword);
  return isPasswordMatch;
};

export const hashRecoveryCode = async (code: string): Promise<string> => {
  const hashedCode = await bcrypt.hash(code, 10);
  return hashedCode;
};

export const compareRecoveryCode = async (
  code: string,
  hashedCode: string,
): Promise<boolean> => {
  const isCodeMatch = await bcrypt.compare(code, hashedCode);
  return isCodeMatch;
};

