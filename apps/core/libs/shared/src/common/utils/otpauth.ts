import { TOTP, Secret } from 'otpauth';

export const generateTOTP = async (
  secret: string,
  label: string,
): Promise<string> => {
  const totp = await initializeTOTP(secret, label);
  return totp.generate();
};

export const verifyTOTP = async (
  secret: string,
  label: string,
  code: string,
): Promise<boolean> => {
  const totp = await initializeTOTP(secret, label);
  const isValid = totp.validate({ token: code.toString(), window: 1 }) !== null;
  return isValid;
};

export const initializeTOTP = async (
  secret: string,
  label: string,
): Promise<TOTP> => {
  return new TOTP({
    secret: Secret.fromBase32(secret),
    issuer: 'Iammoderator',
    label: label,
    digits: 6,
    period: 30,
    algorithm: 'SHA1',
  });
};
