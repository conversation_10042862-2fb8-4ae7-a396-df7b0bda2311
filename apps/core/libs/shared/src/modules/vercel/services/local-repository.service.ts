import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { GitHubService } from '../../github/github.service';
import {
  DeploymentFile,
  LocalRepository,
  RepoConfig,
} from '../interfaces/local-repository.interface';

@Injectable()
export class LocalRepositoryManager {
  private readonly logger = new Logger(LocalRepositoryManager.name);

  constructor(
    private configService: ConfigService,
    private githubService: GitHubService,
  ) {}

  async prepareRepository(
    workspaceId: string,
    repoPath: string,
  ): Promise<LocalRepository> {
    return {
      workspaceId,
      repoId: crypto.randomBytes(8).toString('hex'),
      path: repoPath, // Keep for compatibility
    };
  }

  /**
   * Get files directly from GitHub API and convert to Vercel deployment format
   */
  async packageFiles(
    repo: LocalRepository,
    config?: RepoConfig,
  ): Promise<DeploymentFile[]> {
    try {
      // Get files from GitHub API
      let files = await this.githubService.pullRepository(
        repo.workspaceId,
        config?.outputDirectory || '',
      );

      // Convert files to DeploymentFile format with content as Buffer
      const deploymentFiles: DeploymentFile[] = Object.entries(files).map(
        ([filePath, content]) => ({
          name: filePath,
          content: Buffer.from(content, 'utf-8'), // Convert string to Buffer
        }),
      );

      // Filter out excluded patterns if specified
      if (config?.excludePatterns?.length) {
        return deploymentFiles.filter(
          (file) =>
            !config.excludePatterns?.some((pattern) =>
              file.name.includes(pattern),
            ),
        );
      }

      return deploymentFiles;
    } catch (error) {
      this.logger.error(`Failed to package files: ${error.message}`);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    return;
  }

  async getStatus(): Promise<{ exists: boolean; size: number }> {
    return {
      exists: true,
      size: 0,
    };
  }
}
