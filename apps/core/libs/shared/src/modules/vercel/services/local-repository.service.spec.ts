import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { promises as fs } from 'fs';
import { LocalRepositoryManager } from './local-repository.service';

jest.mock('fs', () => ({
  promises: {
    mkdir: jest.fn(),
    readdir: jest.fn(),
    readFile: jest.fn(),
    stat: jest.fn(),
    rm: jest.fn(),
    rmdir: jest.fn(),
  },
}));

jest.mock('child_process', () => ({
  exec: jest.fn((command, callback) =>
    callback(null, { stdout: '', stderr: '' }),
  ),
}));

describe('LocalRepositoryManager', () => {
  let service: LocalRepositoryManager;
  let configService: ConfigService;
  const mockBaseDir = '/test/vercel-deployments';

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LocalRepositoryManager,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key) => {
              if (key === 'VERCEL_TEMP_STORAGE_PATH') return mockBaseDir;
              return null;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<LocalRepositoryManager>(LocalRepositoryManager);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('prepareRepository', () => {
    const workspaceId = 'test-workspace';
    const repoPath = 'https://github.com/test/repo.git';

    beforeEach(() => {
      (fs.mkdir as jest.Mock).mockResolvedValue(undefined);
    });

    it('should create necessary directories and clone repository', async () => {
      const result = await service.prepareRepository(workspaceId, repoPath);

      expect(fs.mkdir).toHaveBeenCalledWith(
        expect.stringContaining(mockBaseDir),
        expect.any(Object),
      );
      expect(result).toHaveProperty('workspaceId', workspaceId);
      expect(result).toHaveProperty('path');
      expect(result).toHaveProperty('repoId');
    });

    it('should handle errors and cleanup on failure', async () => {
      (fs.mkdir as jest.Mock).mockRejectedValue(
        new Error('Directory creation failed'),
      );

      await expect(
        service.prepareRepository(workspaceId, repoPath),
      ).rejects.toThrow();
    });
  });

  describe('packageFiles', () => {
    const mockRepo = {
      path: '/test/path',
      workspaceId: 'test-workspace',
      repoId: 'test-repo',
    };

    beforeEach(() => {
      (fs.readdir as jest.Mock).mockResolvedValue([
        { name: 'test.js', isDirectory: () => false },
        { name: 'node_modules', isDirectory: () => true },
        { name: 'src', isDirectory: () => true },
      ]);
      (fs.readFile as jest.Mock).mockResolvedValue(Buffer.from('test content'));
    });

    it('should package files excluding specified patterns', async () => {
      const files = await service.packageFiles(mockRepo);

      expect(files.length).toBeGreaterThan(0);
      expect(files[0]).toHaveProperty('name');
      expect(files[0]).toHaveProperty('path');
      expect(files[0]).toHaveProperty('file');
    });

    it('should respect custom exclude patterns', async () => {
      const config = {
        excludePatterns: ['test.js'],
      };

      const files = await service.packageFiles(mockRepo, config);
      expect(files.some((f) => f.name === 'test.js')).toBeFalsy();
    });
  });

  describe('cleanup', () => {
    const mockRepo = {
      path: '/test/path',
      workspaceId: 'test-workspace',
      repoId: 'test-repo',
    };

    beforeEach(() => {
      (fs.rm as jest.Mock).mockResolvedValue(undefined);
      (fs.readdir as jest.Mock).mockResolvedValue([]);
      (fs.rmdir as jest.Mock).mockResolvedValue(undefined);
    });

    it('should remove repository directory', async () => {
      await service.cleanup(mockRepo);

      expect(fs.rm).toHaveBeenCalledWith(
        expect.stringContaining(mockRepo.repoId),
        expect.any(Object),
      );
    });

    it('should attempt to remove empty workspace directory', async () => {
      await service.cleanup(mockRepo);

      expect(fs.rmdir).toHaveBeenCalledWith(
        expect.stringContaining(mockRepo.workspaceId),
      );
    });

    it('should not throw on cleanup failures', async () => {
      (fs.rm as jest.Mock).mockRejectedValue(new Error('Cleanup failed'));

      await expect(service.cleanup(mockRepo)).resolves.not.toThrow();
    });
  });

  describe('getStatus', () => {
    const mockRepo = {
      path: '/test/path',
      workspaceId: 'test-workspace',
      repoId: 'test-repo',
    };

    it('should return status for existing repository', async () => {
      (fs.stat as jest.Mock).mockResolvedValue({ size: 1000 });

      const status = await service.getStatus(mockRepo);

      expect(status.exists).toBeTruthy();
      expect(status.size).toBe(1000);
    });

    it('should return not exists for missing repository', async () => {
      (fs.stat as jest.Mock).mockRejectedValue(new Error('ENOENT'));

      const status = await service.getStatus(mockRepo);

      expect(status.exists).toBeFalsy();
      expect(status.size).toBe(0);
    });
  });
});
