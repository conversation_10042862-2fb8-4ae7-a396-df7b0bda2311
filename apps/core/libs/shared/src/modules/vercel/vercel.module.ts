import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtGuardModule } from '@shared-library/guard/jwt.module';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { SharedGithubModule } from '../github/github.module';
import { WorkspacesModule } from '../workspaces/workspaces.module';
import { LocalRepositoryManager } from './services/local-repository.service';
import { VercelController } from './vercel.controller';
import { VercelService } from './vercel.service';

@Module({
  imports: [ConfigModule, JwtGuardModule, WorkspacesModule, SharedGithubModule],
  controllers: [VercelController],
  providers: [VercelService, PrismaService, LocalRepositoryManager],
  exports: [VercelService, LocalRepositoryManager],
})
export class VercelModule {}
