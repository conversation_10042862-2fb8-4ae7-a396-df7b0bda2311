export interface VercelErrorResponse {
  error?: {
    message: string;
    code?: string;
  };
}

export interface VercelProjectResponse extends VercelErrorResponse {
  id: string;
  name: string;
  framework?: string | null;
  buildCommand?: string | null;
  outputDirectory?: string | null;
  gitRepository?: {
    type: string;
    repo: string;
  };
}

export interface VercelProjectsResponse extends VercelErrorResponse {
  projects: VercelProjectResponse[];
}

export interface VercelFileUploadApiResponse extends VercelErrorResponse {
  url: string;
  sha: string;
}

export interface VercelDeploymentApiResponse extends VercelErrorResponse {
  id: string;
  url: string;
  readyState: string;
  createdAt: string;
  buildingAt?: string;
  ready?: boolean;
}

export interface VercelUserResponse extends VercelErrorResponse {
  id: string;
  email: string;
  name?: string;
  username?: string;
}
