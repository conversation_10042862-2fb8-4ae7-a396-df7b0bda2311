export interface VercelDeploymentRequest {
  workspaceId: string;
  projectName?: string; // Optional, will use repo name if not provided
  framework?: string; // Optional, for framework detection
  environmentVariables?: Record<string, string>; // Optional env vars
  buildCommand?: string; // Optional, custom build command
  outputDirectory?: string; // Optional, custom output directory
}

export interface VercelDeploymentResponse {
  success: boolean;
  projectId?: string;
  deploymentId?: string;
  deploymentUrl?: string;
  error?: string;
}
