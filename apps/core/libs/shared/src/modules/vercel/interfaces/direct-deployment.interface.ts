import { VercelDeploymentResponse } from './vercel-deployment.interface';

export interface DirectDeploymentConfig {
  workspaceId: string;
  projectName?: string;
  framework?: string;
  buildCommand?: string;
  outputDirectory?: string;
  environmentVariables?: Record<string, string>;
}

export interface VercelFileUploadResponse {
  url: string; // The internal Vercel URL for the uploaded file (returned by /v2/files)
  sha: string; // The SHA1 hash of the file content
  size: number; // The size of the file in bytes
  name: string; // The original or desired path/name of the file in the deployment (passed from DeploymentFile)
}

export interface DirectDeploymentResponse extends VercelDeploymentResponse {
  files?: string[];
}

export interface DirectDeploymentOptions {
  target?: 'production' | 'preview';
  teamId?: string;
  isPublic?: boolean;
}
