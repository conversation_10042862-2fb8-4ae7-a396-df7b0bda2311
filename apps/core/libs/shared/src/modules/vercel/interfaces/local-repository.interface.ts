export interface LocalRepository {
  path: string;
  workspaceId: string;
  repoId: string;
}

export interface DeploymentFile {
  name: string; // The desired path/name of the file in the Vercel deployment (e.g., "index.html", "dist/main.js")
  content: Buffer; // The actual content of the file as a Node.js Buffer
}

export interface RepoConfig {
  buildCommand?: string;
  outputDirectory?: string;
  excludePatterns?: string[];
}
