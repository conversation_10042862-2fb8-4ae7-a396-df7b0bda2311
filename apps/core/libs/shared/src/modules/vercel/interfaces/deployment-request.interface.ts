export interface DeploymentRequestData {
  name: string;
  target: 'production' | 'preview';
  files: Array<{
    file: string; // File path
    data: string; // Base64 encoded file content
  }>;
  projectSettings?: {
    framework?: string | null;
    buildCommand?: string | null;
    outputDirectory?: string | null;
    rootDirectory?: string | null;
  };
  env?: Array<{
    key: string;
    value: string;
    target: string[];
  }>;
}
