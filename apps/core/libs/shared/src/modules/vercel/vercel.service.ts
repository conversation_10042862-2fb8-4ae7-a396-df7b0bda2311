import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { WorkspacesService } from '../workspaces/workspaces.service';
import { DeploymentRequestData } from './interfaces/deployment-request.interface';
import {
  DirectDeploymentConfig,
  DirectDeploymentOptions,
  DirectDeploymentResponse,
} from './interfaces/direct-deployment.interface';
import { GitHubRepository } from './interfaces/github-repository.interface';
import { DeploymentFile } from './interfaces/local-repository.interface';
import {
  VercelDeploymentApiResponse,
  VercelErrorResponse,
  VercelProjectResponse,
  VercelProjectsResponse,
  VercelUserResponse,
} from './interfaces/vercel-api.interface';
import {
  VercelConnection,
  VercelTokenResponse,
} from './interfaces/vercel-connection.interface';
import {
  VercelDeploymentRequest,
  VercelDeploymentResponse,
} from './interfaces/vercel-deployment.interface';
import { LocalRepositoryManager } from './services/local-repository.service';

@Injectable()
export class VercelService {
  private readonly VERCEL_API_URL = 'https://api.vercel.com';

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private workspacesService: WorkspacesService,
    private localRepoManager: LocalRepositoryManager,
  ) {}

  async getAuthorizationUrl() {
    const clientId = this.configService.get<string>('VERCEL_CLIENT_ID');
    const redirectUri = this.configService.get<string>('VERCEL_REDIRECT_URI');

    const authUrl = new URL('https://vercel.com/oauth/authorize');
    authUrl.searchParams.append('client_id', clientId);
    authUrl.searchParams.append('redirect_uri', redirectUri);
    authUrl.searchParams.append('response_type', 'code');
    console.log('Vercel Authorization URL:', authUrl.toString());

    return { uri: authUrl.toString() };
  }

  async exchangeCodeForToken(code: string): Promise<VercelTokenResponse> {
    const clientId = this.configService.get<string>('VERCEL_CLIENT_ID');
    const clientSecret = this.configService.get<string>('VERCEL_CLIENT_SECRET');
    const redirectUri = this.configService.get<string>('VERCEL_REDIRECT_URI');

    const response = await fetch(
      `${this.VERCEL_API_URL}/v2/oauth/access_token`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Accept: 'application/json',
        },
        body: new URLSearchParams({
          client_id: clientId,
          client_secret: clientSecret,
          code,
          redirect_uri: redirectUri,
          grant_type: 'authorization_code',
        }),
      },
    );

    const data = (await response.json()) as VercelTokenResponse &
      VercelErrorResponse;
    if (!response.ok) {
      throw new Error(
        data.error?.message || 'Failed to exchange code for token',
      );
    }

    return data;
  }

  async saveVercelConnection(
    workspaceId: string,
    tokenData: VercelTokenResponse,
  ) {
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + 31536000);

    const userResponse = await fetch(`${this.VERCEL_API_URL}/v2/user`, {
      headers: {
        Authorization: `Bearer ${tokenData.access_token}`,
      },
    });
    const user = (await userResponse.json()) as VercelUserResponse;

    try {
      await (this.prisma as any).workspaceVercelConnection.upsert({
        where: { workspaceId },
        create: {
          workspace: {
            connect: { id: workspaceId },
          },
          accessToken: tokenData.access_token,
          refreshToken: tokenData.refresh_token || null,
          expiresAt,
          organization: user,
        },
        update: {
          accessToken: tokenData.access_token,
          refreshToken: tokenData.refresh_token || null,
          expiresAt,
          organization: user,
        },
      });

      return await this.prisma.workspace.findUnique({
        where: { id: workspaceId },
      });
    } catch (error) {
      throw new Error('Failed to save Vercel connection: ' + error.message);
    }
  }

  async getConnection(workspaceId: string) {
    try {
      const connection = await (
        this.prisma as any
      ).workspaceVercelConnection.findUnique({
        where: { workspaceId },
      });

      if (!connection) {
        return { connected: false };
      }

      return { connected: true, connection };
    } catch (error) {
      return { connected: false, error: error.message };
    }
  }

  async getProjects(workspaceId: string) {
    try {
      const connection = await (
        this.prisma as any
      ).workspaceVercelConnection.findUnique({
        where: { workspaceId },
      });

      if (!connection) {
        throw new NotFoundException(
          'No Vercel connection found for this workspace',
        );
      }

      const response = await fetch(`${this.VERCEL_API_URL}/v9/projects`, {
        headers: {
          Authorization: `Bearer ${connection.accessToken}`,
        },
      });

      const data = (await response.json()) as VercelProjectsResponse;

      if (!response.ok) {
        throw new Error(
          data.error?.message || 'Failed to fetch Vercel projects',
        );
      }

      return data;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error('Failed to fetch Vercel projects: ' + error.message);
    }
  }

  async deployRepository(
    data: VercelDeploymentRequest,
  ): Promise<VercelDeploymentResponse> {
    try {
      const workspace = await this.workspacesService.get(data.workspaceId);

      if (!workspace.github) {
        throw new Error('No GitHub repository found for this workspace');
      }

      const githubRepo = workspace.github as unknown as GitHubRepository;

      return await this.deployDirectly(
        githubRepo.html_url,
        {
          workspaceId: data.workspaceId,
          projectName: data.projectName || githubRepo.name,
          framework: data.framework,
          buildCommand: null, // No build command needed for direct deployments
          outputDirectory: null, // No output directory needed
          environmentVariables: data.environmentVariables,
        },
        {
          target: 'production',
        },
      );
    } catch (error) {
      console.error('Error deploying to Vercel:', error);
      return {
        success: false,
        error: error.message || 'Failed to deploy to Vercel',
      };
    }
  }

  async deployDirectly(
    repoPath: string,
    config: DirectDeploymentConfig,
    options: DirectDeploymentOptions = {},
  ): Promise<DirectDeploymentResponse> {
    try {
      const connectionResult = await this.getConnection(config.workspaceId);
      if (!connectionResult.connected || !connectionResult.connection) {
        throw new NotFoundException(
          'No Vercel connection found for this workspace',
        );
      }
      const connection = connectionResult.connection as VercelConnection;

      // First check if project already exists
      const projects = await this.getProjects(config.workspaceId);
      let projectId: string;

      // Look for existing project with the same name
      const existingProject = projects.projects?.find(
        (project) => project.name === config.projectName,
      );

      if (existingProject) {
        console.log('Using existing project:', existingProject.name);
        projectId = existingProject.id;
      } else {
        console.log('Creating new project:', config.projectName);
        const newProject = await this.createProjectWithoutGit(
          connection.accessToken,
          config,
        );
        projectId = newProject.id;
      }

      const localRepo = await this.localRepoManager.prepareRepository(
        config.workspaceId,
        repoPath,
      );

      try {
        const filesToDeploy =
          await this.localRepoManager.packageFiles(localRepo);

        // Use the project (either existing or newly created)
        const deployment = await this.triggerDirectDeployment(
          connection.accessToken,
          projectId,
          filesToDeploy,
          config,
          options,
        );

        return {
          success: true,
          projectId,
          deploymentId: deployment.id,
          deploymentUrl: deployment.url,
          files: filesToDeploy.map((f) => f.name),
        };
      } finally {
        await this.localRepoManager.cleanup();
      }
    } catch (error) {
      console.error('Error in direct deployment:', error);
      return {
        success: false,
        error: error.message || 'Failed to deploy directly to Vercel',
      };
    }
  }

  private async createProjectWithoutGit(
    accessToken: string,
    config: DirectDeploymentConfig,
  ): Promise<VercelProjectResponse> {
    const response = await fetch(`${this.VERCEL_API_URL}/v9/projects`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: config.projectName,
        framework: config.framework || null,
      }),
    });

    const data = (await response.json()) as VercelProjectResponse;
    if (!response.ok) {
      throw new Error(data.error?.message || 'Failed to create Vercel project');
    }

    return data;
  }

  private calculateSha1(buffer: Buffer): string {
    return crypto.createHash('sha1').update(buffer).digest('hex');
  }

  private async triggerDirectDeployment(
    accessToken: string,
    projectId: string,
    files: DeploymentFile[],
    config: DirectDeploymentConfig,
    options: DirectDeploymentOptions,
  ): Promise<VercelDeploymentApiResponse> {
    try {
      // Format the deployment name to comply with Vercel requirements
      const timestamp = new Date()
        .toISOString()
        .replace(/[:.]/g, '-')
        .toLowerCase();
      const deploymentData: DeploymentRequestData = {
        name: `deployment-${timestamp}`,
        target: options.target || 'production',
        files: files.map((f) => {
          // For JSON files, send raw content. For others, use base64
          const isJsonFile = f.name.endsWith('.json');
          return {
            file: f.name,
            data: isJsonFile
              ? f.content.toString('utf-8')
              : f.content.toString('base64'),
          };
        }),
        projectSettings: {
          framework: config.framework || null,
          buildCommand: config.buildCommand || null,
          outputDirectory: config.outputDirectory || null,
          rootDirectory: null,
        },
      };

      if (config.environmentVariables) {
        deploymentData.env = Object.entries(config.environmentVariables).map(
          ([key, value]) => ({
            key,
            value,
            target: ['production', 'preview', 'development'],
          }),
        );
      }

      // Include projectId in the URL path instead of request body
      const response = await fetch(
        `${this.VERCEL_API_URL}/v13/deployments?projectId=${projectId}`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(deploymentData),
        },
      );

      const data = (await response.json()) as VercelDeploymentApiResponse;
      if (!response.ok) {
        throw new Error(data.error?.message || 'Failed to trigger deployment');
      }

      return data;
    } catch (error) {
      throw new Error(`Failed to trigger deployment: ${error.message}`);
    }
  }
}
