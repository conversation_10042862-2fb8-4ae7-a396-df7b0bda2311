import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '@shared-library/guard/jwt.guard';
import { VercelDeploymentRequest } from './interfaces/vercel-deployment.interface';
import { VercelService } from './vercel.service';

@Controller('vercel')
@UseGuards(JwtAuthGuard)
export class VercelController {
  constructor(private readonly vercelService: VercelService) {}

  @Get('authorize')
  async getAuthorizationUrl() {
    try {
      return await this.vercelService.getAuthorizationUrl();
    } catch (error) {
      throw new HttpException(
        'Failed to generate authorization URL',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('callback')
  async exchangeCodeForToken(
    @Body() body: { code: string; workspaceId: string },
  ) {
    try {
      if (!body.code || !body.workspaceId) {
        throw new HttpException(
          'Missing required parameters',
          HttpStatus.BAD_REQUEST,
        );
      }

      const tokenData = await this.vercelService.exchangeCodeForToken(
        body.code,
      );
      return this.vercelService.saveVercelConnection(
        body.workspaceId,
        tokenData,
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to exchange code for token',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('connection')
  async getConnection(@Query('workspaceId') workspaceId: string) {
    try {
      if (!workspaceId) {
        throw new HttpException(
          'Workspace ID is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.vercelService.getConnection(workspaceId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get connection status',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('projects')
  async getProjects(@Query('workspaceId') workspaceId: string) {
    try {
      if (!workspaceId) {
        throw new HttpException(
          'Workspace ID is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.vercelService.getProjects(workspaceId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch Vercel projects',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('deploy')
  async deployRepository(@Body() body: VercelDeploymentRequest) {
    try {
      if (!body.workspaceId) {
        throw new HttpException(
          'Workspace ID is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.vercelService.deployRepository(body);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to deploy to Vercel',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
