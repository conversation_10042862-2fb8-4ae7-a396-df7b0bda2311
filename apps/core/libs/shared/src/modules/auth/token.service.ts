import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { User } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { JwtPayload } from './interfaces/jwt-payload.interface';

@Injectable()
export class TokenService {
  constructor(
    private readonly jwtService: JwtService,
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {}

  /**
   * Generates access and refresh tokens for a user.
   *
   * @param user - The user object containing details like id and email.
   * @returns An object containing accessToken and refreshToken.
   */
  async generateTokens(user: User): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const payload: JwtPayload = {
      userId: user.id,
      email: user.email,
      isEmailVerified: user.is_email_verified,
      isActive: user.is_active,
    };

    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_ACCESS_SECRET'),
      expiresIn: '60m',
    });

    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      expiresIn: '7d',
    });

    await this.saveRefreshToken(user, refreshToken);
    return { accessToken, refreshToken };
  }

  /**
   * Verifies a token and returns the payload if valid.
   *
   * @param token - The token to verify.
   * @param isRefresh - Indicates if the token is a refresh token.
   * @returns The decoded payload if valid.
   * @throws UnauthorizedException if the token is invalid.
   */
  async verifyToken(token: string, isRefresh: boolean = false): Promise<any> {
    try {
      const secret = isRefresh
        ? this.configService.get<string>('JWT_REFRESH_SECRET')
        : this.configService.get<string>('JWT_ACCESS_SECRET');

      return this.jwtService.verify(token, { secret });
    } catch (err) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  /**
   * Hashes a refresh token for secure storage.
   *
   * @param token - The token to hash.
   * @returns A hashed version of the token.
   */
  async hashRefreshToken(token: string): Promise<string> {
    return bcrypt.hash(token, 10);
  }

  /**
   * Compares a refresh token with its stored hash.
   *
   * @param token - The plain token.
   * @param hash - The hashed token from the database.
   * @returns True if the token matches the hash, otherwise false.
   */
  async compareRefreshToken(token: string, hash: string): Promise<boolean> {
    return bcrypt.compare(token, hash);
  }

  async saveRefreshToken(user: User, token: string): Promise<void> {
    const hashedToken = await this.hashRefreshToken(token);
    await this.prisma.refreshToken.create({
      data: {
        user_id: user.id,
        token: hashedToken,
      },
    });
  }

  /**
   * Revokes all refresh tokens for a user.
   *
   * @param user - The user object containing details like id and email.
   * @returns void
   */
  async revokeAllTokens(user: User): Promise<void> {
    await this.prisma.refreshToken.deleteMany({
      where: { user_id: user.id },
    });
  }

  async generateTwoFactorAuthToken(user: User): Promise<{
    accessToken: string;
    refreshToken: string | null;
  }> {
    const payload: JwtPayload = {
      userId: user.id,
      email: user.email,
      isEmailVerified: user.is_email_verified,
      isActive: user.is_active,
    };

    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_2FA_ACCESS_SECRET'),
      expiresIn: '5m',
    });

    return { accessToken, refreshToken: null };
  }
}
