import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UsersModule } from '../users/users.module';
import { PersistenceModule } from 'src/persistence/persistence.module';
import { JwtModule } from '@nestjs/jwt';
import { AuthService } from './auth.service';
import { TokenService } from './token.service';
import { GoogleAuthService } from './oauth/auth-google.service';

@Module({
  imports: [
    UsersModule,
    PersistenceModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_ACCESS_SECRET'),
        signOptions: { expiresIn: '15m' },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [AuthService, TokenService, GoogleAuthService],
  exports: [AuthService, TokenService, GoogleAuthService],
})
export class AuthModule {}
