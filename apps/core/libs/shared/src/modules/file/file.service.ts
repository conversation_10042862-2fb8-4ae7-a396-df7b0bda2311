import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Express } from 'express';
import { promises as fs } from 'fs';
import * as multer from 'multer';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class FileService {
  private readonly storagePath: string;
  private readonly storageType: string;

  constructor() {
    // Define the storage type and path based on environment variables
    this.storageType = process.env.STORAGE_TYPE || 'local'; // Default to 'local'
    this.storagePath = join(process.cwd(), 'file-storage/private');
  }

  /**
   * Uploads the file to the configured storage type.
   * @param file The file object to save (from multer).
   * @returns The public URL to access the uploaded file.
   */
  async uploadFile(file: Express.Multer.File): Promise<string> {
    if (this.storageType === 'local') {
      return this.uploadToLocal(file);
    } else if (this.storageType === 's3') {
      // Placeholder for S3 upload logic
      throw new HttpException(
        'S3 storage is not yet implemented.',
        HttpStatus.NOT_IMPLEMENTED,
      );
    } else {
      throw new HttpException(
        `Storage type '${this.storageType}' is not supported`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Deletes a file from the configured storage type.
   * @param fileName The name of the file to delete.
   */
  async removeFile(fileName: string): Promise<void> {
    if (this.storageType === 'local') {
      return this.removeFromLocal(fileName);
    } else if (this.storageType === 's3') {
      // Placeholder for S3 delete logic
      throw new HttpException(
        'S3 storage is not yet implemented.',
        HttpStatus.NOT_IMPLEMENTED,
      );
    } else {
      throw new HttpException(
        `Storage type '${this.storageType}' is not supported`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Uploads the file to local storage and returns its URL.
   * @param file The file object to save (from multer).
   * @returns The public URL to access the file.
   */
  private async uploadToLocal(file: Express.Multer.File): Promise<string> {
    // Ensure the directory exists
    await this.ensureDirectoryExists();

    // Generate a unique file name
    const uniqueFileName = `${uuidv4()}-${file.originalname}`;
    const filePath = join(this.storagePath, uniqueFileName);

    // Save the file
    try {
      await fs.writeFile(filePath, file.buffer);
    } catch (error) {
      throw new HttpException(
        'Failed to save the file.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    // Return the public URL (assuming local files are served statically)
    return `/files/${uniqueFileName}`;
  }

  /**
   * Deletes a file from local storage.
   * @param fileName The name of the file to delete.
   */
  private async removeFromLocal(fileName: string): Promise<void> {
    const filePath = join(this.storagePath, fileName);
    console.log(filePath);
    try {
      await fs.access(filePath); // Check if the file exists
      await fs.unlink(filePath); // Delete the file
    } catch (error) {
      console.log(error);
      throw new HttpException(
        'File not found or failed to delete.',
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /**
   * Ensures the target directory exists, creating it if necessary.
   */
  private async ensureDirectoryExists(): Promise<void> {
    try {
      await fs.access(this.storagePath);
    } catch {
      await fs.mkdir(this.storagePath, { recursive: true });
    }
  }

  /**
   * Multer configuration for handling file uploads.
   */
  static multerOptions(): multer.Options {
    return {
      storage: multer.memoryStorage(), // Store files in memory temporarily
      limits: {
        fileSize: 5 * 1024 * 1024, // Limit file size to 5MB
      },
    };
  }
}
