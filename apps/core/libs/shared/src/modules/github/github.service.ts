import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Octokit } from '@octokit/rest';
import { EVENTS } from '@shared-library/common/utils/events';
import { WorkspaceCreatedEvent } from '../workspaces/events/workspace.event';
import { WorkspacesService } from '../workspaces/workspaces.service';
import { RepositoryCreatedEvent } from './events/github.event';

interface RepositoryOwner {
  login: string;
  id: number;
  type: string;
}

interface RepositoryDetails {
  id: number;
  name: string;
  owner: RepositoryOwner;
  html_url: string;
  default_branch: string;
}

function isRepositoryDetails(obj: unknown): obj is RepositoryDetails {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'name' in obj &&
    'owner' in obj &&
    'html_url' in obj &&
    'default_branch' in obj &&
    typeof (obj as any).owner === 'object' &&
    (obj as any).owner !== null &&
    'login' in (obj as any).owner
  );
}

interface WorkspaceWithGithub {
  id: string;
  github: RepositoryDetails;
}

@Injectable()
export class GitHubService {
  private readonly octokit: Octokit;
  private readonly baseRepoOwner: string;
  private readonly baseRepoName: string;

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
    private readonly workspacesService: WorkspacesService,
  ) {
    this.octokit = new Octokit({
      auth: this.configService.get<string>('GITHUB_TOKEN'),
    });
    this.baseRepoOwner = this.configService.get<string>('BASE_REPO_OWNER');
    this.baseRepoName = this.configService.get<string>('BASE_REPO_NAME');
  }

  private async getRepositoryDetails(
    workspaceId: string,
  ): Promise<RepositoryDetails> {
    const workspace = await this.workspacesService.get(workspaceId);
    if (!workspace.github || !isRepositoryDetails(workspace.github)) {
      throw new NotFoundException('Repository not found for this workspace');
    }

    // Ensure we have the required fields
    const repo = workspace.github as RepositoryDetails;
    if (!repo.owner?.login) {
      throw new NotFoundException('Repository owner information is missing');
    }

    return {
      id: repo.id,
      name: repo.name,
      owner: {
        login: repo.owner.login,
        id: repo.owner.id,
        type: repo.owner.type,
      },
      html_url: repo.html_url,
      default_branch: repo.default_branch,
    };
  }

  @OnEvent(EVENTS.WORKSPACE_CREATED)
  async handleWorkspaceCreated(event: WorkspaceCreatedEvent) {
    try {
      const newRepoName =
        `${event.name}-${event.userId}-${Date.now()}`.toLowerCase();

      const { data: newRepo } = await this.octokit.repos.createUsingTemplate({
        template_owner: this.baseRepoOwner,
        template_repo: this.baseRepoName,
        name: newRepoName,
        private: true,
      });
      this.eventEmitter.emit(
        EVENTS.REPOSITORY_CREATED,
        new RepositoryCreatedEvent(event.workspaceId, newRepo),
      );
    } catch (error) {
      console.error('Error creating repository:', error);
    }
  }

  /**
   * Push files to a GitHub repository
   * @param workspaceId - Workspace ID
   * @param files - Object containing file paths and contents
   * @param commitMessage - Commit message
   * @param squash - Whether to squash all changes into a single commit
   * @param commits - Array of commits for non-squashed approach
   * @returns Repository URL
   */
  async pushProject(
    workspaceId: string,
    files: Record<string, string>,
    commitMessage: string,
    squash: boolean,
    commits?: Array<{ message: string; timestamp: number }>,
  ): Promise<string> {
    try {
      const repo = await this.getRepositoryDetails(workspaceId);
      console.log(`Pushing to repository: ${repo.owner.login}/${repo.name}`);

      // Get the default branch
      const { data: repoData } = await this.octokit.repos
        .get({
          owner: repo.owner.login,
          repo: repo.name,
        })
        .catch((error) => {
          console.error('Error getting repository details:', error);
          throw new Error(`Failed to get repository details: ${error.message}`);
        });

      const defaultBranch = repoData.default_branch || 'main';
      console.log(`Using default branch: ${defaultBranch}`);

      // Get the latest commit SHA
      const { data: ref } = await this.octokit.git
        .getRef({
          owner: repo.owner.login,
          repo: repo.name,
          ref: `heads/${defaultBranch}`,
        })
        .catch((error) => {
          console.error('Error getting latest commit:', error);
          throw new Error(`Failed to get latest commit: ${error.message}`);
        });

      const baseTreeSha = ref.object.sha;
      console.log(`Base tree SHA: ${baseTreeSha}`);

      if (squash) {
        // Create a single commit with all changes
        console.log('Creating squashed commit...');
        const tree = await this.createTree(repo, files, baseTreeSha);
        console.log(`Created tree with SHA: ${tree.sha}`);

        const commit = await this.createCommit(
          repo,
          commitMessage,
          tree.sha,
          baseTreeSha,
        );
        console.log(`Created commit with SHA: ${commit.sha}`);

        await this.updateRef(repo, defaultBranch, commit.sha);
        console.log(`Updated ref ${defaultBranch} to ${commit.sha}`);
      } else {
        // Create multiple commits based on the provided commit history
        if (!commits || commits.length === 0) {
          throw new Error('Commits are required for non-squashed push');
        }

        console.log(`Creating ${commits.length} commits...`);
        let currentTreeSha = baseTreeSha;
        for (const [index, commit] of commits.entries()) {
          console.log(
            `Creating commit ${index + 1}/${commits.length}: ${commit.message}`,
          );
          const tree = await this.createTree(repo, files, currentTreeSha);
          const newCommit = await this.createCommit(
            repo,
            commit.message,
            tree.sha,
            currentTreeSha,
          );
          currentTreeSha = newCommit.sha;
          console.log(`Created commit with SHA: ${newCommit.sha}`);
        }

        await this.updateRef(repo, defaultBranch, currentTreeSha);
        console.log(`Updated ref ${defaultBranch} to ${currentTreeSha}`);
      }

      return repo.html_url;
    } catch (error) {
      console.error('Error pushing to GitHub:', error);
      if (error.response?.status === 404) {
        throw new Error(
          `Repository not found. Please ensure the repository exists and you have access to it.`,
        );
      } else if (error.response?.status === 403) {
        throw new Error(
          `Permission denied. Please ensure you have write access to the repository.`,
        );
      }
      throw new Error(`Failed to push to GitHub: ${error.message}`);
    }
  }

  /**
   * Pull files from a GitHub repository using Git Trees API for optimal performance
   * @param workspaceId - Workspace ID
   * @param path - Optional path to pull from (currently ignored for full tree)
   * @param recursive - Whether to recursively pull files (always true with tree API)
   * @returns Object containing file paths and contents
   */
  async pullRepository(
    workspaceId: string,
    path: string = '',
    _recursive: boolean = true,
  ): Promise<Record<string, string>> {
    try {
      const repo = await this.getRepositoryDetails(workspaceId);

      // Get the entire tree recursively in one API call (skip getting repo info)
      const { data: tree } = await this.octokit.git.getTree({
        owner: repo.owner.login,
        repo: repo.name,
        tree_sha: repo.default_branch, // Use cached default branch from repo details
        recursive: '1',
      });

      // Filter only files and optionally by path
      let files = tree.tree.filter((item) => item.type === 'blob');

      if (path && path.trim() !== '') {
        files = files.filter((item) => item.path?.startsWith(path));
      }

      // Skip common files that are usually not needed for starter projects
      files = files.filter((item) => {
        const filePath = item.path?.toLowerCase() || '';
        return (
          !filePath.includes('node_modules/') &&
          !filePath.includes('.git/') &&
          !filePath.includes('.next/') &&
          !filePath.includes('dist/') &&
          !filePath.includes('build/') &&
          !filePath.endsWith('.log') &&
          !filePath.includes('.cache/') &&
          !filePath.includes('coverage/')
        );
      });

      // Increase concurrency significantly (15 at a time)
      const result: Record<string, string> = {};
      const concurrencyLimit = 15;

      // Process all files in parallel
      const allPromises: Promise<void>[] = [];

      for (let i = 0; i < files.length; i += concurrencyLimit) {
        const batch = files.slice(i, i + concurrencyLimit);

        const batchPromise = Promise.all(
          batch.map(async (file) => {
            try {
              if (!file.path || !file.sha) return;

              const { data: blob } = await this.octokit.git.getBlob({
                owner: repo.owner.login,
                repo: repo.name,
                file_sha: file.sha,
              });

              result[file.path] = Buffer.from(blob.content, 'base64').toString(
                'utf-8',
              );
            } catch (error) {
              // Skip failed files
            }
          }),
        ).then(() => {}); // Convert to void

        allPromises.push(batchPromise);
      }

      // Wait for all batches to complete
      await Promise.all(allPromises);

      return result;
    } catch (error) {
      throw new Error(`Failed to pull from GitHub: ${error.message}`);
    }
  }

  private async createTree(
    repo: RepositoryDetails,
    files: Record<string, string>,
    baseTreeSha: string,
  ) {
    try {
      const tree = await this.octokit.git.createTree({
        owner: repo.owner.login,
        repo: repo.name,
        base_tree: baseTreeSha,
        tree: Object.entries(files).map(([path, content]) => ({
          path,
          mode: '100644',
          type: 'blob',
          content,
        })),
      });
      return tree.data;
    } catch (error) {
      console.error('Error creating tree:', error);
      throw new Error(`Failed to create tree: ${error.message}`);
    }
  }

  private async createCommit(
    repo: RepositoryDetails,
    message: string,
    treeSha: string,
    parentSha: string,
  ) {
    try {
      const commit = await this.octokit.git.createCommit({
        owner: repo.owner.login,
        repo: repo.name,
        message,
        tree: treeSha,
        parents: [parentSha],
      });
      return commit.data;
    } catch (error) {
      console.error('Error creating commit:', error);
      throw new Error(`Failed to create commit: ${error.message}`);
    }
  }

  private async updateRef(repo: RepositoryDetails, ref: string, sha: string) {
    try {
      await this.octokit.git.updateRef({
        owner: repo.owner.login,
        repo: repo.name,
        ref: `heads/${ref}`,
        sha,
      });
    } catch (error) {
      console.error('Error updating ref:', error);
      throw new Error(`Failed to update ref: ${error.message}`);
    }
  }
}
