import { CreateEngagespotClient, EngagespotClient } from '@engagespot/node';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

interface Recipient {
  identifier: string;
  email?: string;
  phoneNumber?: string;
}

@Injectable()
export class EngagespotService {
  private client: CreateEngagespotClient;

  constructor(private configService: ConfigService) {
    this.client = EngagespotClient({
      apiKey: this.configService.get('ENGAGESPOT_API_KEY'),
      apiSecret: this.configService.get('ENGAGESPOT_API_SECRET'),
    });
  }

  /**
    Send a notification to one or more recipients
    @param notification - The notification payload
    @param recipients - Array of recipient identifiers (usually email addresses)
    @returns Promise resolving to the send result
  */
  async send(
    workflowIdentifier: string,
    recipients: Recipient[],
    data: any | null = null,
  ) {
    return this.client
      .send({
        notification: {
          workflow: {
            identifier: workflowIdentifier,
          },
        },
        sendTo: {
          recipients,
        },
        data,
      })
      .catch(() => {
        throw new Error('Failed to send notification');
      });
  }
}
