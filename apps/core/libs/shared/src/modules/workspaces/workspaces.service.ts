import { Injectable, NotFoundException } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { EVENTS } from '@shared-library/common/utils/events';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { RepositoryCreatedEvent } from '../github/events/github.event';
import { UserCreatedEvent } from '../users/events/user.event';
import { WorkspaceCreatedEvent } from './events/workspace.event';
import { IAcceptInvitation } from './interfaces/accept-invitation.interface';
import { ICreateWorkspace } from './interfaces/create.interface';
import { IInviteMember } from './interfaces/invite-member.interface';
import { IUpdateWorkspaceSetting } from './interfaces/update-setting.interface';

@Injectable()
export class WorkspacesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Create a new workspace.
   * @param data - Workspace data
   * @returns Workspace
   */
  async create(data: ICreateWorkspace) {
    const workspace = await this.prisma.workspace.create({
      data: {
        name: data.name,
        owner_id: data.ownerId,
      },
    });

    this.eventEmitter.emit(
      EVENTS.WORKSPACE_CREATED,
      new WorkspaceCreatedEvent(workspace.id, data.ownerId, data.name),
    );

    return workspace;
  }

  /**
   * Get a workspace by ID.
   * @param id - Workspace ID
   * @returns Workspace
   */
  async get(id: string) {
    const workspace = await this.prisma.workspace.findFirst({
      where: {
        id,
      },
    });

    if (!workspace) {
      throw new NotFoundException('Workspace not found for id: ' + id);
    }

    return workspace;
  }

  /**
   * List all workspaces for a user.
   * @param userId - User ID
   * @returns Array of workspaces ordered by creation date (oldest first, newest last)
   */
  async listAllWorkspaces(userId: number) {
    return this.prisma.workspace.findMany({
      where: {
        OR: [{ owner_id: userId }, { members: { some: { user_id: userId } } }],
      },
      include: {
        members: {
          include: {
            user: true,
            role: true,
          },
        },
        owner: true,
      },
      orderBy: {
        created_at: 'asc',
      },
    });
  }

  /**
   * Get workspace members.
   * @param workspaceId - Workspace ID
   * @param userId - User ID
   * @returns Workspace members
   */
  getMembers(workspaceId: string, userId: number) {
    return this.prisma.workspaceMember.findMany({
      where: {
        workspace: {
          id: workspaceId,
          owner_id: userId,
        },
      },
      include: {
        user: true,
      },
    });
  }

  /**
   * Invite a member to a workspace.
   * @param data - Invite member data
   * @returns Workspace member invitation
   */
  async inviteMember(data: IInviteMember) {
    const workspace = await this.get(data.workspaceId);

    const role = await this.prisma.workspaceRole
      .findFirstOrThrow({
        where: {
          name: data.role,
        },
      })
      .catch(() => {
        throw new NotFoundException('Role not found');
      });

    return this.prisma.workspaceMemberInvitation.create({
      data: {
        workspace_id: workspace.id,
        role_id: role.id,
        invited_by_id: data.ownerId,
      },
    });
  }

  /**
   * Accept a workspace member invitation.
   * @param data - Accept invitation data
   * @returns Workspace member
   */
  async acceptMemberInvitation(data: IAcceptInvitation) {
    const invitation = await this.prisma.workspaceMemberInvitation
      .findFirstOrThrow({
        where: {
          code: data.code,
        },
        include: {
          role: true,
          workspace: true,
        },
      })
      .catch(() => {
        throw new NotFoundException('Invitation not found');
      });

    return this.prisma.workspaceMember.create({
      data: {
        workspace_id: invitation.workspace_id,
        workspace_role_id: invitation.role_id,
        user_id: data.userId,
      },
      include: {
        user: true,
      },
    });
  }

  /**
   * Remove a member from a workspace.
   * @param workspaceId - Workspace ID
   * @param ownerId - Owner ID
   * @param memberId - Member ID
   * @returns Removed member
   */
  removeMember(workspaceId: string, ownerId: number, memberId: string) {
    return this.prisma.workspaceMember.delete({
      where: {
        id: memberId,
        workspace: {
          id: workspaceId,
          owner_id: ownerId,
        },
      },
    });
  }

  /**
   * Get workspace settings.
   * @param workspaceId - Workspace ID
   * @param userId - User ID
   * @returns Workspace settings
   */
  getSettings(workspaceId: string, userId: number) {
    return this.prisma.workspaceSettings.findMany({
      where: {
        workspace: {
          id: workspaceId,
          owner_id: userId,
        },
      },
    });
  }

  async updateSetting(data: IUpdateWorkspaceSetting) {
    // Find setting by workspace ID and key.
    const setting = await this.prisma.workspaceSettings.findFirst({
      where: {
        workspace: {
          id: data.workspaceId,
          owner_id: data.userId,
        },
        key: data.key,
      },
    });

    // Update setting if it exists.
    if (setting) {
      return this.prisma.workspaceSettings.update({
        where: {
          id: setting.id,
        },
        data: {
          value: data.value,
        },
      });
    }

    // Create setting if it doesn't exist.
    return this.prisma.workspaceSettings.create({
      data: {
        workspace_id: data.workspaceId,
        key: data.key,
        value: data.value,
      },
    });
  }

  @OnEvent('repository.created')
  async handleRepositoryCreated(event: RepositoryCreatedEvent) {
    try {
      await this.prisma.workspace.update({
        where: {
          id: event.workspaceId,
        },
        data: {
          github: event.repository,
        },
      });

      console.log('Repository created successfully');
    } catch (error) {
      console.error('Error creating repository:', error);
    }
  }

  @OnEvent(EVENTS.USER_CREATED)
  async handleUserCreated(event: UserCreatedEvent) {
    const randomName = Math.random().toString(36).substring(2, 15);
    this.create({
      name: `project-${randomName}`,
      ownerId: event.userId,
    });
  }
}
