import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtGuardModule } from '@shared-library/guard/jwt.module';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { WorkspacesModule } from '../workspaces/workspaces.module';
import { SupabaseController } from './supabase.controller';
import { SupabaseService } from './supabase.service';

@Module({
  imports: [ConfigModule, JwtGuardModule, WorkspacesModule],
  controllers: [SupabaseController],
  providers: [SupabaseService, PrismaService],
  exports: [SupabaseService],
})
export class SupabaseModule {}
