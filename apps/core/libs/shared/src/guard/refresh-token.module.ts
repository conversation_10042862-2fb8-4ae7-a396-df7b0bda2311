import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PersistenceModule } from 'src/persistence/persistence.module';

const modulesInRefreshTokenGuard = [
  JwtModule.register({
    /* JWT module configuration */
  }),
  PersistenceModule,
];

@Module({
  imports: modulesInRefreshTokenGuard,
  exports: modulesInRefreshTokenGuard,
})
export class RefreshTokenGuardModule {}

