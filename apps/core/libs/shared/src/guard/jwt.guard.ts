import { Injectable } from '@nestjs/common';
import {
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { JwtPayload } from '@shared-library/modules/auth/interfaces/jwt-payload.interface';
import e from 'express';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = request.headers['authorization']?.split(' ')[1];

    if (!token) {
      throw new UnauthorizedException('Authorization token is missing');
    }

    try {
      const payload = await this.jwtService.verifyAsync<JwtPayload>(token, {
        secret: this.configService.get<string>('JWT_ACCESS_SECRET'),
      });
      if (!payload.isActive) {
        throw new UnauthorizedException('User is not active');
      }

      if (!payload.isEmailVerified) {
        throw new UnauthorizedException('User email is not verified');
      }

      request.user = payload;
      return true;
    } catch (error: any) {
      throw new UnauthorizedException(error.message || 'Invalid token');
    }
  }
}
