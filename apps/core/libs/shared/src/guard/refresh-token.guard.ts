import { Injectable } from '@nestjs/common';
import {
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { TokenService } from '@shared-library/modules/auth/token.service';
import { JwtPayload } from '@shared-library/modules/auth/interfaces/jwt-payload.interface';

@Injectable()
export class RefreshTokenGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly tokenService: TokenService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = request.headers['authorization']?.split(' ')[1];

    if (!token) {
      throw new UnauthorizedException('Refresh token is missing');
    }

    try {
      const payload = await this.jwtService.verifyAsync<JwtPayload>(token, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      });

      const refreshTokenRecords = await this.prisma.refreshToken.findMany({
        where: { user_id: payload.userId },
      });

      if (refreshTokenRecords.length === 0) {
        throw new UnauthorizedException('No refresh tokens found for user');
      }

      let isValid = false;
      for (const refreshToken of refreshTokenRecords) {
        if (
          await this.tokenService.compareRefreshToken(token, refreshToken.token)
        ) {
          isValid = true;

          await this.prisma.refreshToken.delete({
            where: { id: refreshToken.id },
          });
          break;
        }
      }

      if (!isValid) {
        throw new UnauthorizedException('Invalid or expired refresh token');
      }

      request.user = payload;

      return true;
    } catch (error) {
      throw new UnauthorizedException(
        error.message || 'Invalid or expired refresh token',
      );
    }
  }
}
