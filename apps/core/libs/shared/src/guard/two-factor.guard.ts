import { Injectable } from '@nestjs/common';
import {
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { TokenService } from '@shared-library/modules/auth/token.service';
import { JwtPayload } from '@shared-library/modules/auth/interfaces/jwt-payload.interface';

@Injectable()
export class TwoFactorGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = request.headers['authorization']?.split(' ')[1];

    if (!token) {
      throw new UnauthorizedException('two-factor token is missing');
    }

    try {
      const payload = await this.jwtService.verifyAsync<JwtPayload>(token, {
        secret: this.configService.get<string>('JWT_2FA_ACCESS_SECRET'),
      });

      request.user = payload;

      return true;
    } catch (error) {
      throw new UnauthorizedException(
        error.message || 'Invalid or expired two - factor token',
      );
    }
  }
}
