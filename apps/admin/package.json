{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "pnpm eslint --fix", "preview": "vite preview"}, "dependencies": {"@ai-sdk/react": "^1.2.8", "@babel/parser": "^7.27.4", "@hookform/resolvers": "^3.9.1", "@isomorphic-git/lightning-fs": "^4.6.2", "@lexical/file": "^0.32.1", "@lexical/list": "^0.31.2", "@lexical/react": "^0.31.2", "@lexical/rich-text": "^0.31.2", "@lexical/selection": "^0.31.2", "@lexical/text": "^0.31.2", "@lexical/utils": "^0.31.2", "@lukemorales/query-key-factory": "^1.3.4", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.62.7", "@tanstack/react-table": "^8.20.5", "axios": "^1.7.9", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "input-otp": "^1.4.1", "isomorphic-git": "^1.30.1", "js-cookie": "^3.0.5", "lexical": "^0.31.2", "lucide-react": "^0.468.0", "motion": "^11.14.4", "next": "^15.3.2", "next-themes": "^0.4.4", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-error-boundary": "^4.1.2", "react-hook-form": "^7.54.0", "react-router": "^7.0.2", "react-use-websocket": "^4.13.0", "remeda": "^2.17.4", "repomix": "^0.3.7", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-resize-observer": "^9.1.0", "zod": "^3.24.1", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.15.0", "@shikijs/monaco": "^3.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.12.0", "postcss": "^8.4.49", "shiki": "^3.2.2", "tailwindcss": "^3.4.16", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}, "packageManager": "pnpm@9.0.0"}