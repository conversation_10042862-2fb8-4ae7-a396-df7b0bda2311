# Design System Documentation

## 1. Color Palette

### **Base Colors**

- **background:** `#FFFFFF` (--background: 0 0% 100%)
- **foreground:** `#020617` (--foreground: 222.2 84% 4.9%)
- **card:** `#FFFFFF` (--card: 0 0% 100%)
- **card-foreground:** `#020617` (--card-foreground: 222.2 84% 4.9%)
- **popover:** `#FFFFFF` (--popover: 0 0% 100%)
- **popover-foreground:** `#020617` (--popover-foreground: 222.2 84% 4.9%)
- **primary:** `#006AE8` (--primary: 211 100% 45%)
- **primary-foreground:** `#F8FAFC` (--primary-foreground: 210 40% 98%)
- **secondary:** `#B3D4FA` (--secondary: 211 60% 85%)
- **secondary-foreground:** `#1E293B` (--secondary-foreground: 222.2 47.4% 11.2%)
- **muted:** `#B3D4FA` (--muted: 211 60% 85%)
- **muted-foreground:** `#8C959F` (--muted-foreground: 220, 16%, 66%)
- **muted-primary:** `#7D8590` (--muted-primary: 233, 7%, 49%)
- **muted-secondary:** `#BDBDBD` (--muted-secondary: 0, 0%, 74%)
- **muted-accent:** `#F7F7F7` (--muted-accent: 0, 0%, 97%)
- **accent:** `#B3D4FA` (--accent: 211 60% 85%)
- **accent-foreground:** `#1E293B` (--accent-foreground: 222.2 47.4% 11.2%)
- **destructive:** `#E11D48` (--destructive: 0 84.2% 60.2%)
- **destructive-foreground:** `#F8FAFC` (--destructive-foreground: 210 40% 98%)
- **border:** `#D9E8FC` (--border: 211 60% 90%)
- **input:** `#D9E8FC` (--input: 211 60% 90%)
- **ring:** `#006AE8` (--ring: 211 100% 45%)

### **Additional Colors**

- **label:** `#2B3442` (--label-primary: 212, 13%, 19%)

### **Chart Colors**

- **chart-1:** `#004FB5` (--chart-1: 211 100% 40%)
- **chart-2:** `#2A9D8F` (--chart-2: 173 58% 39%)
- **chart-3:** `#264653` (--chart-3: 197 37% 24%)
- **chart-4:** `#E9C46A` (--chart-4: 43 74% 66%)
- **chart-5:** `#F4A261` (--chart-5: 27 87% 67%)

### **Sidebar Colors**

- **sidebar:** `#FAFAFA` (--sidebar-background: 0 0% 98%)
- **sidebar-foreground:** `#3F3F46` (--sidebar-foreground: 240 5.3% 26.1%)
- **sidebar-primary:** `#18181B` (--sidebar-primary: 240 5.9% 10%)
- **sidebar-primary-foreground:** `#FAFAFA` (--sidebar-primary-foreground: 0 0% 98%)
- **sidebar-accent:** `#F4F4F5` (--sidebar-accent: 240 4.8% 95.9%)
- **sidebar-accent-foreground:** `#18181B` (--sidebar-accent-foreground: 240 5.9% 10%)
- **sidebar-border:** `#E4E4E7` (--sidebar-border: 220 13% 91%)
- **sidebar-ring:** `#3B82F6` (--sidebar-ring: 217.2 91.2% 59.8%)

---

## 2. Typography

### **Font Families**

- **Primary:** Nunito
- **Secondary:** Inter
- **Tertiary:** Nunito Sans
- **Display:** Gilfroy-ExtraBold
- **Light Text:** Gilfroy-Light

### **Font Sizes**

| Size          | px   | rem       |
| ------------- | ---- | --------- |
| **xs**        | 12px | 0.75rem   |
| **xs-plus**   | 13px | 0.8125rem |
| **sm**        | 14px | 0.875rem  |
| **sm-plus**   | 15px | 0.9375rem |
| **base**      | 16px | 1rem      |
| **base-plus** | 17px | 1.0625rem |
| **lg**        | 18px | 1.125rem  |
| **lg-plus**   | 19px | 1.1875rem |
| **xl**        | 20px | 1.25rem   |
| **xl-plus**   | 21px | 1.3125rem |
| **2xl**       | 24px | 1.5rem    |
| **3xl**       | 30px | 1.875rem  |
| **4xl**       | 36px | 2.25rem   |

### **Font Weights**

- **Light:** 300
- **Regular:** 400
- **Medium:** 500
- **Semibold:** 600
- **Bold:** 700
- **Extrabold:** 800

### **Line Heights**

- **Tight:** 1.25
- **Normal:** 1.5
- **Relaxed:** 1.75

---

## 3. Spacing

Based on a 4px grid:

| Scale  | px   | rem     |
| ------ | ---- | ------- |
| **0**  | 0px  | 0rem    |
| **1**  | 4px  | 0.25rem |
| **2**  | 8px  | 0.5rem  |
| **3**  | 12px | 0.75rem |
| **4**  | 16px | 1rem    |
| **5**  | 20px | 1.25rem |
| **6**  | 24px | 1.5rem  |
| **8**  | 32px | 2rem    |
| **10** | 40px | 2.5rem  |
| **12** | 48px | 3rem    |
| **16** | 64px | 4rem    |
| **20** | 80px | 5rem    |
| **24** | 96px | 6rem    |

---

## 4. Border Radius

- **Small (sm):** calc(var(--radius) - 4px)
- **Medium (md):** calc(var(--radius) - 2px)
- **Large (lg):** var(--radius)

**Default Radius Value:** 0.5rem

---

## 5. Breakpoints

| Breakpoint | px     |
| ---------- | ------ |
| **sm**     | 640px  |
| **md**     | 768px  |
| **lg**     | 1024px |
| **xl**     | 1280px |
| **2xl**    | 1536px |

**Custom Height Breakpoints:**

- **media-h-sm:** 640px
- **media-h-sm-plus:** 700px
- **media-h-md:** 768px

---

## 6. Component Color Mapping

1. **Button**

   - **primary**
     - Background: `#006AE8` (--primary)
     - Text: `#F8FAFC` (--primary-foreground)
   - **secondary**
     - Background: `#B3D4FA` (--secondary)
     - Text: `#1E293B` (--secondary-foreground)
   - **destructive**
     - Background: `#E11D48` (--destructive)
     - Text: `#F8FAFC` (--destructive-foreground)
   - **outline**
     - Background: `#FFFFFF` (--background)
     - Border: `#D9E8FC` (--input)
     - Text: `#020617` (--foreground)
     - Hover Background: `#B3D4FA` (--accent)
     - Hover Text: `#1E293B` (--accent-foreground)
   - **ghost**
     - Background: Transparent
     - Text: `#020617` (--foreground)
     - Hover Background: `#B3D4FA` (--accent)
     - Hover Text: `#1E293B` (--accent-foreground)

2. **Input**

   - Background: `#FFFFFF` (--background)
   - Border: `#D9E8FC` (--input)
   - Text: `#020617` (--foreground)
   - Focus Ring: `#006AE8` (--ring)

3. **Card**

   - Background: `#FFFFFF` (--card)
   - Text: `#020617` (--card-foreground)

4. **Dialog/Modal**

   - Background: `#FFFFFF` (--background)
   - Border: `#D9E8FC` (--border)
   - Text: `#020617` (--foreground)

5. **Dropdown Menu**

   - Background: `#FFFFFF` (--popover)
   - Text: `#020617` (--popover-foreground)

6. **Checkbox**

   - Background (unchecked): `#FFFFFF` (--background)
   - Background (checked): `#006AE8` (--primary)
   - Border: `#006AE8` (--primary)
   - Checkmark: `#F8FAFC` (--primary-foreground)

7. **Radio**

   - Background (unchecked): `#FFFFFF` (--background)
   - Background (checked): `#006AE8` (--primary)
   - Border: `#006AE8` (--primary)
   - Dot: `#F8FAFC` (--primary-foreground)

8. **Toggle**

   - Background (off): `#D9E8FC` (--input)
   - Background (on): `#006AE8` (--primary)
   - Handle: `#FFFFFF`

9. **Tooltip**

   - Background: `#FFFFFF` (--popover)
   - Text: `#020617` (--popover-foreground)
   - Border: `#D9E8FC` (--border)

10. **Alert**

    - **default**
      - Background: `#FFFFFF` (--background)
      - Text: `#020617` (--foreground)
    - **destructive**
      - Background: `#E11D48` (--destructive)
      - Text: `#F8FAFC` (--destructive-foreground)

11. **Badge**

    - **default**
      - Background: `#006AE8` (--primary)
      - Text: `#F8FAFC` (--primary-foreground)
    - **secondary**
      - Background: `#B3D4FA` (--secondary)
      - Text: `#1E293B` (--secondary-foreground)
    - **destructive**
      - Background: `#E11D48` (--destructive)
      - Text: `#F8FAFC` (--destructive-foreground)

12. **Slider**

    - Track: `#B3D4FA` (--secondary)
    - Range: `#006AE8` (--primary)
    - Thumb: `#FFFFFF` (--background)

13. **Progress**

    - Background: `#B3D4FA` (--secondary)
    - Foreground: `#006AE8` (--primary)

14. **Tabs**

    - **Inactive**
      - Text: `#8C959F` (--muted-foreground)
    - **Active**
      - Background: `#FFFFFF` (--background)
      - Text: `#020617` (--foreground)
    - **Hover**
      - Background: `#B3D4FA` (--accent)

15. **Accordion**

    - Background: `#FFFFFF` (--background)
    - Text: `#020617` (--foreground)
    - Border: `#D9E8FC` (--border)

16. **Popover**

    - Background: `#FFFFFF` (--popover)
    - Text: `#020617` (--popover-foreground)
    - Border: `#D9E8FC` (--border)

17. **Command (Command Palette)**

    - Background: `#FFFFFF` (--popover)
    - Text: `#020617` (--popover-foreground)
    - **Selected Item**
      - Background: `#B3D4FA` (--accent)
      - Text: `#1E293B` (--accent-foreground)
    - **Input**
      - Background: Transparent
      - Text: `#020617` (--foreground)

18. **Separator**
    - Color: `#D9E8FC` (--border)

---

## 7. Shadows

| Level       | Values                                                              |
| ----------- | ------------------------------------------------------------------- |
| **sm**      | 0 1px 2px 0 rgb(0 0 0 / 0.05)                                       |
| **DEFAULT** | 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)       |
| **md**      | 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)    |
| **lg**      | 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)  |
| **xl**      | 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) |

---

## 8. Icons

Use **Lucide React** icons for consistency throughout the project.

---
