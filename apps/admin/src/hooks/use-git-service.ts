import { createGitService, type GitService } from '@/lib/git-service'; // Adjusted path
import { useBaseStore } from '@/store/base-store';
import { useMemo } from 'react';

/**
 * Custom hook to get an instance of the GitService.
 * The service is memoized and will be re-created if the active workspace ID changes.
 * It also attempts to initialize the git repository for the workspace when the service is first created
 * or when the workspace ID changes.
 *
 * @returns {GitService | null} An instance of the GitService, or null if no active workspace ID is found.
 */
export function useGitService(): GitService | null {
  // Assuming your Zustand store for workspace has `activeWorkspace` which has an `id`
  // Adjust the selector as per your actual store structure.
  const activeWorkspaceId = useBaseStore((state) => state.workspaceId);

  const gitService = useMemo(() => {
    if (activeWorkspaceId) {
      try {
        const service = createGitService(activeWorkspaceId);
        return service;
      } catch (error) {
        console.error(
          `Failed to create GitService for workspace ${activeWorkspaceId}:`,
          error,
        );
        return null;
      }
    }
    return null;
  }, [activeWorkspaceId]);

  return gitService;
}
