import { useCallback, useRef } from 'react';

function useDebounceFn<T extends (...args: any) => void>(
  fn: T,
  delay: number,
): T {
  // Ref to store the timeout ID
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // useCallback ensures debounced function is stable
  const debouncedFn = useCallback(
    (...args: any) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current); // Clear previous timeout
      }

      // Set a new timeout
      timeoutRef.current = setTimeout(() => {
        fn(...args); // Call the original function after delay
      }, delay);
    },
    [fn, delay], // Re-create debounced function when fn or delay changes
  );

  return debouncedFn as T;
}

export default useDebounceFn;
