import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ChevronDown } from 'lucide-react';
import { useState } from 'react';

import { cn } from '@/lib/utils';
import { ProfileForm } from './components/profile-form';

export default function Workspace() {
  const [activeTab, setActiveTab] = useState('profile');

  const tabs = [
    { value: 'profile', label: 'Profile' },
    { value: 'billing', label: 'Billing' },
    { value: 'delete', label: 'Delete Account' },
  ];

  return (
    <div className="flex bg-[#f1f5f9]">
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="bg-white min-h-[80vh] rounded-xl shadow-sm p-4 sm:p-6 border border-[#E2E8F0]">
            <h1 className="text-xl sm:text-2xl font-semibold  text-[#23405e] mb-4 sm:mb-6">
              Workspace Manager
            </h1>

            <Tabs
              value={activeTab}
              onValueChange={(value) => {
                setActiveTab(value);
              }}
              className="w-full"
            >
              <div className="relative mb-4 sm:mb-6">
                <TabsList className="relative z-10 flex sm:hidden justify-between items-center p-2 bg-[#f1f5f9] rounded-md">
                  <span className="text-sm font-medium text-[#23405e]">
                    {tabs.find((tab) => tab.value === activeTab)?.label}
                  </span>
                  <ChevronDown className="h-4 w-4 text-[#23405e]" />
                </TabsList>

                <TabsList className="absolute top-full left-0 right-0 z-20 flex-col sm:relative sm:flex sm:flex-row border-b border-[#e2e8f0] w-fit justify-start rounded-none bg-white sm:bg-transparent p-0 h-auto overflow-hidden sm:overflow-x-auto">
                  {tabs.map((tab) => (
                    <TabsTrigger
                      key={tab.value}
                      value={tab.value}
                      className={cn(
                        'w-full sm:w-auto rounded-none border-b-2 border-transparent px-4 py-2 text-[#9D9DBE] data-[state=active]:border-[#016AE8] data-[state=active]:text-[#016AE8] data-[state=active]:bg-white sm:data-[state=active]:bg-transparent data-[state=active]:font-medium data-[state=active]:shadow-none whitespace-nowrap text-sm',
                        tab.value === 'delete' &&
                          'text-red-500 data-[state=active]:border-red-500 data-[state=active]:text-red-500',
                      )}
                    >
                      {tab.label}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>

              <TabsContent value="profile" className="mt-4 sm:mt-6">
                <ProfileForm />
              </TabsContent>

              <TabsContent value="billing" className="mt-4 sm:mt-6">
                {null}
              </TabsContent>

              <TabsContent value="delete" className="mt-4 sm:mt-6">
                {null}
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </div>
  );
}
