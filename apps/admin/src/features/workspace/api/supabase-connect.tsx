import { api } from '@/lib/api-client';
import { useBaseStore } from '@/store/base-store';
import { useMutation, useQuery } from '@tanstack/react-query';

// Types
type AuthorizeResponse = {
  uri: string;
  codeVerifier: string;
};

type CallbackRequest = {
  code: string;
  codeVerifier: string;
  workspaceId: string;
};

export type SupabaseConnection = {
  id: string;
  workspaceId: string;
  organizationId: string;
  project: {
    id: string;
    name: string;
    ref: string;
    region: string;
    status: 'ACTIVE_HEALTHY' | 'ACTIVE_UNHEALTHY' | 'DELETED';
    created_at: string;
    api: {
      anon_key: string;
      service_role_key: string;
    };
  };
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
};

type ConnectionResponse = {
  connected: boolean;
  connection?: SupabaseConnection;
};

// API functions
const getAuthorizationUrl = async (): Promise<AuthorizeResponse> => {
  return api.get('/supabase/authorize');
};

export const exchangeSupabaseCodeForToken = async (
  values: CallbackRequest,
): Promise<{ success: boolean; connection: SupabaseConnection }> => {
  return api.post('/supabase/callback', values);
};

export const migrateModuleSql = async (values: {
  moduleId: string;
  workspaceId: string;
}): Promise<any> => {
  return api.post('/project-planning/migrate-module-sql', values);
};

const getConnection = async (
  workspaceId: string,
): Promise<ConnectionResponse> => {
  return api.get('/supabase/connection', {
    params: { workspaceId },
  });
};

// Hooks
export const useSupabaseAuthorize = () => {
  return useMutation({
    mutationFn: getAuthorizationUrl,
  });
};

export const useSupabaseExchangeCodeForToken = () => {
  return useMutation({
    mutationFn: exchangeSupabaseCodeForToken,
  });
};

export const useSupabaseConnection = () => {
  const { workspaceId } = useBaseStore();

  return useQuery({
    queryKey: ['supabase-connection', workspaceId],
    queryFn: () => getConnection(workspaceId as string),
    enabled: !!workspaceId,
  });
};
