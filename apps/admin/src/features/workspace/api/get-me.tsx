import { api } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export interface GetMeResponse {
  id: number;
  email: string;
  is_email_verified: boolean;
  is_active: boolean;
  is_two_factor_enabled: boolean;
  name: string;
  workspaces: Workspace[];
}

export interface Workspace {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
  deleted_at: any;
  owner_id: number;
  metadata: any;
  github: any;
}

const getMe = async (): Promise<GetMeResponse> => {
  const { data } = await api.get('/auth/me');
  return data;
};

export const useGetMe = () => {
  return useQuery({
    queryKey: ['/auth/me'],
    queryFn: getMe,
  });
};
