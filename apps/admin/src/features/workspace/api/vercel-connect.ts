import { api } from '@/lib/api-client';
import { useBaseStore } from '@/store/base-store';
import { useMutation, useQuery } from '@tanstack/react-query';

const VERCEL_CLIENT_SLUG = 'fullstackfox';

// Types
export type VercelConnection = {
  id: string;
  workspaceId: string;
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  project: {
    id: string;
    name: string;
    framework: string;
    status: string;
  };
  accessToken: string;
  refreshToken?: string;
  expiresAt: Date;
};

type ConnectionResponse = {
  connected: boolean;
  connection?: VercelConnection;
};

type CallbackRequest = {
  code: string;
  workspaceId: string;
};

// API functions
const getAuthorizationUrl = async () => {
  // Create CSRF token
  const state = generateRandomString(16);
  localStorage.setItem('latestCSRFToken', state);

  // Return the Vercel integration URL
  return {
    uri: `https://vercel.com/integrations/${VERCEL_CLIENT_SLUG}/new?state=${state}`,
  };
};

export const exchangeVercelCodeForToken = async (
  values: CallbackRequest,
): Promise<{ success: boolean; connection: VercelConnection }> => {
  return api.post('/vercel/callback', values);
};

const getConnection = async (
  workspaceId: string,
): Promise<ConnectionResponse> => {
  return api.get('/vercel/connection', {
    params: { workspaceId },
  });
};

// Helper function to generate random string for CSRF token
const generateRandomString = (length: number) => {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array)
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');
};

// Hooks
export const useVercelAuthorize = () => {
  return useMutation({
    mutationFn: getAuthorizationUrl,
  });
};

export const useVercelExchangeCodeForToken = () => {
  return useMutation({
    mutationFn: exchangeVercelCodeForToken,
  });
};

export const useVercelConnection = () => {
  const { workspaceId } = useBaseStore();

  return useQuery({
    queryKey: ['vercel-connection', workspaceId],
    queryFn: () => getConnection(workspaceId as string),
    enabled: !!workspaceId,
  });
};
