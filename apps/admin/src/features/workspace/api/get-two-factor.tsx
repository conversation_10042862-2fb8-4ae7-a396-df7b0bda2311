import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { GetTwoFactorAuthResponse } from '../types';

const getTwoFactorCode = async (): Promise<GetTwoFactorAuthResponse> => {
  const { data } = await api.get('/auth/two-factor-auth');
  return data;
};

export const useGetTwoFactorCode = () => {
  return useMutation({
    mutationFn: getTwoFactorCode,
  });
};
