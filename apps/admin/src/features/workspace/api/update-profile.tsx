import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

type UpdateProfileParams = {
  name: string;
};

type UpdateProfileResponse = {
  success: boolean;
  message: string;
};

const updateProfile = async ({
  name,
}: UpdateProfileParams): Promise<UpdateProfileResponse> => {
  const { data } = await api.put<UpdateProfileResponse>('/api/profile', {
    name,
  });
  return data;
};

export const useUpdateProfile = () => {
  return useMutation({ mutationFn: updateProfile });
};
