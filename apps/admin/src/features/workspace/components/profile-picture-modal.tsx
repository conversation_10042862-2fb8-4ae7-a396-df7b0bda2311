import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

export const ProfilePictureModal = ({
  isImagePreviewOpen,
  setIsImagePreviewOpen,
  fileInputRef,
  selectedImage,
  setSelectedImage,
}: {
  isImagePreviewOpen: boolean;
  setIsImagePreviewOpen: (isOpen: boolean) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  selectedImage: string | null;
  setSelectedImage: (imageUrl: string | null) => void;
}) => {
  const handleUpdateImage = () => {
    // Here you would typically call an API to update the image

    // After successful update, you might want to update the avatar image
    setIsImagePreviewOpen(false);
  };

  const handleCloseImagePreview = () => {
    setIsImagePreviewOpen(false);
    setSelectedImage(null);
  };

  return (
    <Dialog open={isImagePreviewOpen} onOpenChange={setIsImagePreviewOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Preview Image</DialogTitle>
          <DialogDescription>
            Preview your new profile picture before updating.
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          {selectedImage && (
            <img
              src={selectedImage}
              alt="Preview"
              className="w-64 h-64 object-cover rounded-full mx-auto"
            />
          )}
        </div>
        <div className="flex flex-col sm:flex-row justify-end gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            className="w-full sm:w-auto"
          >
            Select New Image
          </Button>
          <Button
            variant="outline"
            onClick={handleCloseImagePreview}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button onClick={handleUpdateImage} className="w-full sm:w-auto">
            Update Image
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
