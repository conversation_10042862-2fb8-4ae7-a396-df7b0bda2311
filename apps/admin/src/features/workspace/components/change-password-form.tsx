import { LoadingSpinner } from '@/components/loading/main';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { errorParser } from '@/lib/errors';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { UseMutationResult } from '@tanstack/react-query';
import { Eye, EyeOff } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { ChangePassword, useChangePassword } from '../api/change-password';

const passwordChangeSchema = z
  .object({
    currentPassword: z.string().min(8, {
      message: 'Current password must be at least 8 characters.',
    }),
    newPassword: z.string().min(8, {
      message: 'New password must be at least 8 characters.',
    }),
    confirmNewPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmNewPassword, {
    path: ['confirmNewPassword'],
    message: 'New password and confirm new password must match.',
  });

type PasswordChange = {
  currentPassword: boolean;
  newPassword: boolean;
  confirmNewPassword: boolean;
};

export const ChangePasswordForm = () => {
  const form = useForm<z.infer<typeof passwordChangeSchema>>({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmNewPassword: '',
    },
  });

  const changePassword = useChangePassword();

  const onSubmit = (values: z.infer<typeof passwordChangeSchema>) => {
    changePassword.mutate(
      {
        currentPassword: values.currentPassword,
        newPassword: values.newPassword,
      },
      {
        onSuccess: () => {
          toast.success('Password updated successfully');
        },
        onError: (error) => {
          toast.error(errorParser(error, 'message'));
        },
      },
    );
  };

  const [showPassword, setShowPassword] = React.useState({
    currentPassword: false,
    newPassword: false,
    confirmNewPassword: false,
  });

  return (
    <div className="space-y-4 sm:space-y-6 w-2/5">
      <div>
        <h2 className="text-base sm:text-lg font-medium text-[#23405e] mb-1 sm:mb-2">
          Password Settings
        </h2>
        <p className="text-xs sm:text-sm  mb-4 sm:mb-6 text-[#8D8D8D] font-nunito">
          Instantly update and manage your account password
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="currentPassword"
            render={({ field }) => (
              <PasswordInput
                field={field}
                changePassword={changePassword}
                showPassword={showPassword}
                setShowPassword={setShowPassword}
                type="currentPassword"
              />
            )}
          />

          <FormField
            control={form.control}
            name="newPassword"
            render={({ field }) => (
              <PasswordInput
                field={field}
                changePassword={changePassword}
                showPassword={showPassword}
                setShowPassword={setShowPassword}
                type="newPassword"
              />
            )}
          />

          <FormField
            control={form.control}
            name="confirmNewPassword"
            render={({ field }) => (
              <PasswordInput
                field={field}
                changePassword={changePassword}
                showPassword={showPassword}
                setShowPassword={setShowPassword}
                type="confirmNewPassword"
              />
            )}
          />

          <Button
            disabled={changePassword.isPending}
            className="w-full font-nunito-sans"
            type="submit"
          >
            Change password
            {changePassword.isPending && <LoadingSpinner />}
          </Button>
        </form>
      </Form>
    </div>
  );
};

const PasswordInput = ({
  showPassword,
  field,
  changePassword,
  type,
  setShowPassword,
}: {
  showPassword: PasswordChange;
  setShowPassword: React.Dispatch<React.SetStateAction<PasswordChange>>;
  field: any;
  changePassword: UseMutationResult<string[], Error, ChangePassword, unknown>;
  type: keyof PasswordChange;
}) => {
  const label =
    type === 'confirmNewPassword'
      ? 'Confirm New Password'
      : type === 'newPassword'
        ? 'New Password'
        : 'Current Password';

  const placeholder =
    type === 'confirmNewPassword'
      ? 'Re-enter your new password'
      : type === 'newPassword'
        ? 'Enter your new password'
        : 'Enter your current password';

  return (
    <FormItem>
      <FormLabel>{label}</FormLabel>
      <FormControl>
        <div className="relative">
          <Input
            type={showPassword?.[type] ? 'text' : 'password'}
            placeholder={placeholder}
            {...field}
            onChange={(e) => {
              changePassword.reset();
              field.onChange(e);
            }}
          />
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="absolute right-2 top-1/2 -translate-y-1/2"
            onClick={() =>
              setShowPassword((state) => ({
                ...state,
                [type]: !state?.[type],
              }))
            }
          >
            {showPassword?.[type] ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
            <span className="sr-only">
              {showPassword?.[type] ? 'Hide password' : 'Show password'}
            </span>
          </Button>
        </div>
      </FormControl>
      <FormMessage />
    </FormItem>
  );
};
