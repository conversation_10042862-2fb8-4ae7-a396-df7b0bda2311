import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import React from 'react';

interface VercelDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  connection: {
    id: string;
    accessToken: string;
    organization: any;
    project: any;
  } | null;
  isOperational: boolean;
}

export const VercelDetailsModal: React.FC<VercelDetailsModalProps> = ({
  open,
  onOpenChange,
  connection,
  isOperational,
}) => {
  if (!connection) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-[#141A22] text-white border-[#1C2026] max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Vercel Connection Details
          </DialogTitle>
          <DialogDescription className="text-[#BABABA]">
            View your Vercel connection information
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-[#BABABA]">
              Connection Status
            </h3>
            <div className="flex items-center gap-2">
              <div className="text-[#016AE8]">Connected</div>
              {isOperational && (
                <div className="bg-[#DCFCE7] text-[#16A34A] px-2 py-1 rounded text-xs">
                  Active
                </div>
              )}
            </div>
          </div>

          {connection.organization && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-[#BABABA]">
                Organization
              </h3>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm">Name:</div>
                <div className="text-sm text-[#BABABA]">
                  {connection.organization.name || 'N/A'}
                </div>
                <div className="text-sm">ID:</div>
                <div className="text-sm text-[#BABABA]">
                  {connection.organization.id || 'N/A'}
                </div>
              </div>
            </div>
          )}

          {connection.project && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-[#BABABA]">Project</h3>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-sm">Name:</div>
                <div className="text-sm text-[#BABABA]">
                  {connection.project.name || 'N/A'}
                </div>
                <div className="text-sm">ID:</div>
                <div className="text-sm text-[#BABABA]">
                  {connection.project.id || 'N/A'}
                </div>
                <div className="text-sm">Framework:</div>
                <div className="text-sm text-[#BABABA]">
                  {connection.project.framework || 'N/A'}
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
