import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import React from 'react';
import { SupabaseConnection } from '../api/supabase-connect';

interface SupabaseDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  connection: SupabaseConnection | null;
  isOperational?: boolean;
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const SupabaseDetailsModal: React.FC<SupabaseDetailsModalProps> = ({
  open,
  onOpenChange,
  connection,
  isOperational = false,
}) => {
  if (!connection) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[450px] p-0 bg-[#141A22] border-[#1C2026] text-white">
        <DialogHeader className="px-6 pt-6 pb-4">
          <DialogTitle className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-[#3ECF8E] to-[#2FB574] rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-base font-bold text-white">S</span>
            </div>
            <div>
              <div className="text-xl font-semibold text-white">
                {connection?.project?.name}
              </div>
              <div className="text-sm text-[#BABABA] font-normal">
                Supabase Project
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="px-6 pb-6">
          <div className="space-y-6">
            {/* Project Info Section */}
            <div className="bg-[#10151C] rounded-lg p-4 border border-[#1C2026]">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <label className="text-xs font-medium text-[#BABABA] uppercase tracking-wide">
                    Region
                  </label>
                  <div className="text-sm font-medium text-white">
                    {connection?.project?.region}
                  </div>
                </div>

                <div className="space-y-1">
                  <label className="text-xs font-medium text-[#BABABA] uppercase tracking-wide">
                    Created
                  </label>
                  <div className="text-sm text-white">
                    {connection?.project?.created_at
                      ? formatDate(connection?.project?.created_at)
                      : ''}
                  </div>
                </div>
              </div>
            </div>

            {/* Status Section */}
            <div className="bg-[#10151C] rounded-lg p-4 border border-[#1C2026]">
              <div className="space-y-2">
                <label className="text-xs font-medium text-[#BABABA] uppercase tracking-wide">
                  Connection Status
                </label>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-[#016AE8] rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium text-[#016AE8]">
                      Connected
                    </span>
                  </div>
                  {isOperational && (
                    <Badge
                      variant="outline"
                      className="bg-[#DCFCE7] text-[#16A34A] border-[#16A34A]/20 px-2 py-1"
                    >
                      Operational
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
