import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { useBaseStore } from '@/store/base-store';
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router';
import {
  exchangeSupabaseCodeForToken,
  migrateModuleSql,
  useSupabaseAuthorize,
  useSupabaseConnection,
} from '../api/supabase-connect';
import SupabaseLogo from '../assets/supabse.svg';
import { SupabaseDetailsModal } from './supabase-details-modal';

interface SupabaseConnectProps {
  highlighted?: boolean;
}

export const SupabaseConnect: React.FC<SupabaseConnectProps> = ({
  highlighted = false,
}) => {
  const {
    workspaceId,
    supabaseCodeVerifier,
    setSupabaseCodeVerifier,
    moduleId,
  } = useBaseStore();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [modalOpen, setModalOpen] = useState(false);

  const supabaseConnection = useSupabaseConnection();
  const supabaseAuthorize = useSupabaseAuthorize();
  const code = searchParams.get('code');
  const service = searchParams.get('service');
  const isSupabaseService = service === 'supabase';
  const [isLoading, setIsLoading] = React.useState(false);

  useEffect(() => {
    const exchangeSupabaseToken = async () => {
      if (
        code &&
        supabaseCodeVerifier &&
        workspaceId &&
        !isLoading &&
        isSupabaseService
      ) {
        try {
          setIsLoading(true); // Prevent multiple calls

          await exchangeSupabaseCodeForToken({
            code,
            codeVerifier: supabaseCodeVerifier,
            workspaceId,
          });

          if (moduleId && workspaceId) {
            try {
              await migrateModuleSql({
                moduleId,
                workspaceId,
              });
            } catch (error: any) {
              console.error('Migration error:', error);
            }
          }

          toast.success('Supabase connected successfully!');
          setIsLoading(false);
          setSupabaseCodeVerifier(null);
          supabaseConnection.refetch();
          navigate('/integrations');
        } catch (error: any) {
          console.error('Connection error:', error);
          setIsLoading(false);
          navigate('/integrations');
          return;
        }
      }
    };

    exchangeSupabaseToken();
  }, [
    code,
    isLoading,
    isSupabaseService,
    navigate,
    setSupabaseCodeVerifier,
    supabaseCodeVerifier,
    supabaseConnection,
    workspaceId,
    moduleId,
  ]);

  const handleConnect = () => {
    supabaseAuthorize.mutate(undefined, {
      onSuccess: (data) => {
        setSupabaseCodeVerifier(data.codeVerifier);

        if (data.uri) {
          window.open(data.uri, '_blank', 'noopener,noreferrer');
        } else {
          console.error('No URI received from authorization response');
        }
      },
      onError: (error) => {
        console.error('Authorization error:', error);
      },
    });
  };

  if (supabaseConnection.isLoading) {
    const SkeletonCard = () => (
      <div className="bg-[#141A22] rounded-[14px] p-1 w-full">
        <div className="p-4">
          <div className="flex justify-between items-center mb-[1.75px]">
            <div className="flex items-center gap-[6px]">
              <Skeleton className="w-6 h-6 rounded-lg bg-[#2A2F36]" />
              <Skeleton className="h-4 w-24 bg-[#2A2F36]" />
            </div>
          </div>

          <div className="bg-[#10151C] border border-[#1C2026] rounded-xl">
            <div className="flex justify-between items-center h-[74px] px-[14px]">
              <div className="flex items-center gap-4">
                <div className="space-y-2">
                  <Skeleton className="h-3 w-20 bg-[#2A2F36]" />
                  <Skeleton className="h-3 w-16 bg-[#2A2F36]" />
                </div>
              </div>
              <Skeleton className="h-8 w-16 bg-[#2A2F36]" />
            </div>
          </div>
        </div>
      </div>
    );

    return (
      <div className="w-full grid grid-cols-3 gap-4 col-span-3">
        {Array.from({ length: 3 }).map((_, index) => (
          <SkeletonCard key={index} />
        ))}
      </div>
    );
  }

  const isConnected = supabaseConnection.data?.connected;
  const connection = supabaseConnection.data?.connection;
  const isOperational =
    supabaseConnection.data?.connection?.project?.status === 'ACTIVE_HEALTHY';

  return (
    <>
      <div
        className={cn(
          'bg-[#141A22] rounded-[14px] p-1 w-full',
          highlighted && 'border-2 border-primary/30',
        )}
      >
        {/* Testing */}
        <div className="p-4">
          {/* Header with logo and title */}
          <div className="flex justify-between items-center mb-[1.75px]">
            <div className="flex items-center gap-[8px] mb-4">
              <img src={SupabaseLogo} className="" alt="" />
              <span className="text-white text-[14.88px] font-semibold leading-[21px]">
                Supabase
              </span>
            </div>
          </div>

          {/* Content area */}
          <div className="bg-[#10151C] border border-[#1C2026] rounded-xl">
            <div className="flex justify-center items-center h-[74px]">
              <div className="flex justify-between items-center w-full px-[14px]">
                <div className="flex items-center gap-4">
                  <div>
                    <div className="text-[14px] font-medium text-[#BABABA] leading-[14px]">
                      Account Syn
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      {!isConnected ? (
                        <div className="text-[12px] text-[#9E3E01] leading-[20px]">
                          Not Connected
                        </div>
                      ) : (
                        <>
                          <div className="text-[12px] text-[#016AE8] leading-[20px]">
                            Connected
                          </div>
                          {isOperational && (
                            <div className="bg-[#DCFCE7] text-[#16A34A] px-[6px] py-[2px] rounded text-[9.69px] font-medium leading-[13px]">
                              Operational
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {!isConnected ? (
                  <Button
                    className="bg-[#CB5002] hover:bg-[#B8460B] text-white px-4 py-1 text-[14px] font-medium leading-[24px] rounded-[6px] h-auto flex justify-center items-center"
                    onClick={handleConnect}
                    disabled={isLoading || supabaseAuthorize.isPending}
                  >
                    {isLoading || supabaseAuthorize.isPending
                      ? 'Connecting...'
                      : 'Connect'}
                  </Button>
                ) : (
                  <Button
                    className="bg-[#CB5002] hover:bg-[#B8460B] text-white px-4 py-1 text-[14px] font-medium leading-[24px] rounded-[6px] h-auto flex justify-center items-center"
                    onClick={() => setModalOpen(true)}
                  >
                    view
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Details Modal */}
      <SupabaseDetailsModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        connection={connection || null}
        isOperational={isOperational}
      />
    </>
  );
};
