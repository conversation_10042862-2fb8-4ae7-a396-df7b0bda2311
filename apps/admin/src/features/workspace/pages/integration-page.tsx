import DashboardHeader from '@/components/shared/DashboardHeader';
import React from 'react';
import { useSearchParams } from 'react-router';
import { SupabaseConnect } from '../components/supabase-connect';
import { VercelConnect } from '../components/vercel-connect';

const IntegrationsPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const highlighted = searchParams.get('highlighted');

  return (
    <div className="flex flex-col h-screen bg-[#0f1114] text-white">
      <DashboardHeader />
      <div className="flex-1 p-6">
        <div className="space-y-6">
          <div className="space-y-[17px]">
            <h2 className="text-base font-normal text-[#F4F4F5]">Database</h2>
            <div className="grid grid-cols-3 gap-[14px]">
              <SupabaseConnect highlighted={highlighted === 'supabase'} />
            </div>
          </div>

          <div className="space-y-[17px]">
            <h2 className="text-base font-normal text-[#F4F4F5]">Deployment</h2>
            <div className="grid grid-cols-3 gap-[14px]">
              <VercelConnect />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntegrationsPage;
