import { Editor } from '@/components/blocks/editor-x/editor';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  <PERSON><PERSON>ooter,
  CardHeader,
} from '@/components/ui/card';
import { Form, FormField } from '@/components/ui/form';
import { cn } from '@/lib/utils';
import { SerializedEditorState } from 'lexical';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';

interface EditorPanelProps {
  isOpen: boolean;
  initialContent: SerializedEditorState;
  onSave: (content: SerializedEditorState) => void;
  onCancel: () => void;
}

type EditorForm = {
  editor: SerializedEditorState;
};

export const EditorPanel = ({
  isOpen,
  initialContent,
  onSave,
  onCancel,
}: EditorPanelProps) => {
  const submitRef = useRef<HTMLButtonElement | null>(null);
  const [dataLoaded, setDataLoaded] = useState(false);
  const form = useForm<EditorForm>({
    defaultValues: {
      editor: initialContent,
    },
  });

  // Update form when initialContent changes
  useEffect(() => {
    if (!dataLoaded) {
      form.reset({
        editor: initialContent,
      });

      setDataLoaded(true);
    }
  }, [initialContent, form, isOpen, dataLoaded]);

  const handleSave = (values: EditorForm) => {
    onSave(values.editor);
  };

  return (
    <div
      className={cn(
        'transform transition-all duration-300 h-full flex flex-col',
        isOpen
          ? 'translate-x-0 opacity-100 w-full'
          : 'translate-x-full opacity-0 w-0 overflow-hidden',
      )}
    >
      <Card className="w-full bg-[#10151c] border-[#363b42] text-white h-full flex flex-col overflow-hidden">
        {/* Header */}
        <CardHeader className="pb-6 flex-shrink-0">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-normal text-white">
                Product Features
              </h3>
            </div>
          </div>
        </CardHeader>

        {/* Editor Form */}
        <CardContent className="flex-1 overflow-hidden pt-2 pb-4">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSave)}
              className="h-full flex flex-col"
            >
              <div className="flex-1 mb-4 overflow-hidden">
                <FormField
                  control={form.control}
                  name="editor"
                  render={({ field }) => (
                    <div className="h-full text-xs-plus">
                      <Editor
                        editorSerializedState={field.value}
                        onSerializedChange={(value) => {
                          field.onChange(value);
                        }}
                      />
                    </div>
                  )}
                />
              </div>

              {/* Action Buttons */}
              <button ref={submitRef} type="submit" className="hidden">
                submit
              </button>
            </form>
          </Form>
        </CardContent>

        <CardFooter className="flex flex-col gap-4 flex-shrink-0">
          <div className="w-full flex justify-end font-lato">
            <div className="flex gap-4 flex-shrink-0 w-full md:w-1/2 ">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="flex-1 bg-[#21262D] border-[#363b42] text-white hover:bg-[#21262D]/90 hover:border-[#3e3e3e]"
              >
                Close
              </Button>
              <Button
                onClick={() => submitRef.current?.click()}
                className="flex-1 bg-[#A9490C] hover:bg-[#A9490C]/90 border border-[#FE8336] text-white"
              >
                Save
              </Button>
            </div>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};
