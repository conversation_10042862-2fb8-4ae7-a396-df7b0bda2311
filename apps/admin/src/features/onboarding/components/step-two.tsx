import { LoadingSpinner } from '@/components/loading/main';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import * as z from 'zod';
import { useGetOnboardingProgress } from '../hooks/get-onboarding-progress';

const onboardingSchema = z.object({
  brandColor: z.string(),
  brandVoice: z.string(),
});

export const OnboardingStepTwo = ({
  onSubmit,
  isPending,
}: {
  onSubmit: (values: Record<string, string>) => Promise<void>;
  isPending: boolean;
}) => {
  const getOnboardingProgress = useGetOnboardingProgress();
  const onboardingData = getOnboardingProgress.data?.data;
  const form = useForm<z.infer<typeof onboardingSchema>>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: {
      brandColor: '#ff8c42',
      brandVoice: 'professional',
    },
  });
  const { reset } = form;

  // Use useWatch instead of useState to track the color value
  const brandColor = useWatch({
    control: form.control,
    name: 'brandColor',
  });

  const colorSuggestions = [
    { id: 1, color: '#ff8c42' }, // Orange
    { id: 2, color: '#6366f1' }, // Purple/Blue
    { id: 3, color: '#4ade80' }, // Green
    { id: 4, color: '#86efac' }, // Light Green
  ];

  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    form.setValue('brandColor', e.target.value, { shouldValidate: true });
  };

  const handleSuggestionClick = (color: string) => {
    form.setValue('brandColor', color, { shouldValidate: true });
  };

  const isValidHexColor = (color: string) => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
  };

  useEffect(() => {
    reset({
      brandColor: onboardingData?.brandColor || '',
      brandVoice: onboardingData?.brandVoice || '',
    });
  }, [reset, onboardingData?.brandColor, onboardingData?.brandVoice]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-12 font-nunito"
      >
        <div className="flex flex-col gap-4">
          <FormLabel className="text-white-200 font-normal">
            Got a brand color? Let us know your primary one!
          </FormLabel>

          <div>
            <div className="flex relative items-center border rounded-lg p-2 bg-black-100">
              <div
                className="w-7 h-6 rounded-md mr-4"
                style={{ backgroundColor: brandColor }}
              />

              <input
                type="text"
                {...form.register('brandColor', {
                  required: 'Brand color is required',
                  validate: (value) =>
                    isValidHexColor(value) ||
                    'Please enter a valid hex color (e.g., #FF5733)',
                })}
                className="bg-transparent text-gray-400 text-sm focus:outline-none w-full placeholder:text-[#737373]"
                placeholder="Enter Colour Code"
              />
              <input
                type="color"
                value={brandColor}
                onChange={handleColorChange}
                className="opacity-0 absolute w-8 h-8 left-3 cursor-pointer"
                aria-label="Select color"
              />
            </div>

            {form.formState.errors.brandColor && (
              <p className="text-red-500 mt-2 text-sm">
                {form.formState.errors.brandColor.message}
              </p>
            )}
          </div>

          <div className="flex flex-col gap-2">
            <h2 className="text-[#737373] text-sm">Suggestions</h2>
            <div className="flex space-x-4">
              {colorSuggestions.map((suggestion) => (
                <button
                  key={suggestion.id}
                  type="button"
                  onClick={() => handleSuggestionClick(suggestion.color)}
                  className="w-8 h-8 rounded-md border border-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-400"
                  style={{ backgroundColor: suggestion.color }}
                  aria-label={`Select color ${suggestion.color}`}
                />
              ))}
            </div>
          </div>
        </div>

        <FormField
          control={form.control}
          name="brandVoice"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-white-200 font-normal">
                How does your brand like to sound?
              </FormLabel>

              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="bg-black-100 h-10">
                    <SelectValue placeholder="Select a verified email to display" />
                  </SelectTrigger>
                </FormControl>

                <SelectContent>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="friendly">Friendly</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          disabled={isPending}
          className="w-full font-nunito-sans"
          type="submit"
        >
          Continue
          {isPending && <LoadingSpinner />}
        </Button>
      </form>
    </Form>
  );
};
