import { LoadingSpinner } from '@/components/loading/main';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useGetOnboardingProgress } from '../hooks/get-onboarding-progress';

const onboardingSchema = z.object({
  name: z.string().min(2, {
    message: 'Name must be at least 2 characters.',
  }),
  appDescription: z.string().min(4, {
    message: 'App description must be at least 8 characters.',
  }),
});

export const OnboardingStepOne = ({
  onSubmit,
  isPending,
}: {
  onSubmit: (values: Record<string, string>) => Promise<void>;
  isPending: boolean;
}) => {
  const getOnboardingProgress = useGetOnboardingProgress();
  const onboardingData = getOnboardingProgress.data?.data;
  const form = useForm<z.infer<typeof onboardingSchema>>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: {
      name: '',
      appDescription: '',
    },
  });
  const { reset } = form;

  useEffect(() => {
    reset({
      name: onboardingData?.name || '',
      appDescription: onboardingData?.appDescription || '',
    });
  }, [reset, onboardingData?.appDescription, onboardingData?.name]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-12 font-nunito"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-white-200 font-normal">
                Tell us your app name
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter your app name"
                  className="bg-black-100"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="appDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-white-200 font-normal">
                What does your app do ?
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Textarea
                    rows={4}
                    placeholder="Enter your app description in great detail"
                    className="bg-black-100"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          disabled={isPending}
          className="w-full font-nunito-sans"
          type="submit"
        >
          Continue
          {isPending && <LoadingSpinner />}
        </Button>
      </form>
    </Form>
  );
};
