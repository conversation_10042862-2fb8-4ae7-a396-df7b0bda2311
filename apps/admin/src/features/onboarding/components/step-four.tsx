import { env } from '@/config/env';
import { cookie } from '@/lib/cookie';
import { cn } from '@/lib/utils';
import { useChat } from '@ai-sdk/react';
import { SerializedEditorState } from 'lexical';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useGetOnboardingProgress } from '../hooks/get-onboarding-progress';
import { useLexicalTextConverter } from '../hooks/lexical';
import { BlueprintCard } from './blueprint-card';
import { EditorPanel } from './editor-panel';

const initialLexicalConfig = {
  root: {
    children: [
      {
        children: [
          {
            detail: 0,
            format: 0,
            mode: 'normal',
            style: '',
            text: 'No config found',
            type: 'text',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'paragraph',
        version: 1,
      },
    ],
    direction: 'ltr',
    format: '',
    indent: 0,
    type: 'root',
    version: 1,
  },
} as unknown as SerializedEditorState;

interface BlueprintData {
  appName: string;
  features: Array<{
    name: string;
    description: string;
  }>;
}

export const OnboardingStepFour = ({
  onSubmit,
  isPending: _isPending,
}: {
  onSubmit: (values: Record<string, string>) => Promise<void>;
  isPending: boolean;
}) => {
  const { extractText, createLexicalFromText } = useLexicalTextConverter();
  const [blueprint, setBlueprint] = useState<BlueprintData | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const token = cookie.get('access_token');
  const getOnboardingProgress = useGetOnboardingProgress();
  const hasGeneratedRef = useRef(false);

  const { messages, append, status } = useChat({
    api: `${env.API_URL}/chat/generate-blueprint`,
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    onError: (error) => {
      console.error('Blueprint generation error:', error);
    },
    onFinish: (message) => {
      const blueprintData = parseBlueprintFromMessage(message.content);
      if (blueprintData) {
        // Always use the name from onboarding data instead of AI-generated name
        const correctedBlueprint = {
          ...blueprintData,
          appName:
            getOnboardingProgress.data?.data?.name || blueprintData.appName,
        };
        setBlueprint(correctedBlueprint);
      }
    },
  });

  const handleGenerateBlueprint = useCallback(() => {
    setBlueprint(null);

    // Use append to send the message with onboarding config
    append({
      role: 'user',
      content: JSON.stringify({ onboardingConfig: getOnboardingProgress.data }),
    });
  }, [append, getOnboardingProgress.data]);

  useEffect(() => {
    // Prevent generation if already generated, streaming, or messages exist
    if (
      hasGeneratedRef.current ||
      status === 'streaming' ||
      messages.length > 0
    ) {
      return;
    }

    // Set the ref to true to indicate generation has started
    hasGeneratedRef.current = true;
    handleGenerateBlueprint();
    // The 'status' variable from useChat (e.g., 'streaming', 'idle')
    // should be used to reflect loading states in the UI if needed.
  }, [handleGenerateBlueprint, status, messages.length]);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768); // Common breakpoint for mobile
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Parse blueprint data from AI message (JSON format)
  const parseBlueprintFromMessage = (content: string): BlueprintData | null => {
    try {
      // Try to find JSON in the message content
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (parsed.appName && parsed.features) {
          return {
            appName: parsed.appName,
            features: parsed.features,
          };
        }
      }
    } catch {
      // If parsing fails, return null
    }
    return null;
  };

  // Parse blueprint data from plain text format
  const parseBlueprintFromPlainText = (
    content: string,
  ): BlueprintData | null => {
    try {
      const lines = content
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line);

      // Find app name
      const appNameLine = lines.find((line) => line.startsWith('App Name:'));
      if (!appNameLine) return null;

      const appName = appNameLine.replace('App Name:', '').trim();
      if (!appName) return null;

      // Find features section
      const featuresStartIndex = lines.findIndex(
        (line) =>
          line.toLowerCase().includes('core features') ||
          line.toLowerCase().includes('features'),
      );

      if (featuresStartIndex === -1) return null;

      // Parse features (look for bullet points)
      const features: Array<{ name: string; description: string }> = [];

      for (let i = featuresStartIndex + 1; i < lines.length; i++) {
        const line = lines[i];

        // Match bullet features like "• Feature Name: Description"
        const bulletMatch = line.match(/^•\s*([^:]+):\s*(.+)$/);
        // Also match numbered features for backward compatibility
        const numberedMatch = line.match(/^\d+\.\s*([^:]+):\s*(.+)$/);

        if (bulletMatch) {
          const [, name, description] = bulletMatch;
          features.push({
            name: name.trim(),
            description: description.trim(),
          });
        } else if (numberedMatch) {
          const [, name, description] = numberedMatch;
          features.push({
            name: name.trim(),
            description: description.trim(),
          });
        }
      }

      if (features.length === 0) return null;

      return {
        appName,
        features,
      };
    } catch (error) {
      console.error('Error parsing plain text blueprint:', error);
      return null;
    }
  };

  // Get streamed content from the latest message
  const streamedContent =
    messages.length > 0 ? messages[messages.length - 1]?.content || '' : '';

  const handleEditBlueprint = () => {
    setIsEditing(!isEditing);
  };

  const handleSaveEdit = (editedContent: SerializedEditorState) => {
    // Extract text from the lexical editor state
    const plainText = extractText(editedContent);

    // Try to parse the edited content as blueprint data
    // First try JSON format (for AI-generated content)
    let updatedBlueprint = parseBlueprintFromMessage(plainText);

    // If JSON parsing fails, try plain text format (for user-edited content)
    if (!updatedBlueprint) {
      updatedBlueprint = parseBlueprintFromPlainText(plainText);
    }

    if (updatedBlueprint) {
      // Always use the name from onboarding data instead of parsed name
      const correctedBlueprint = {
        ...updatedBlueprint,
        appName:
          getOnboardingProgress.data?.data?.name || updatedBlueprint.appName,
      };
      setBlueprint(correctedBlueprint);
    }

    setIsEditing(false);
  };

  const handleStartBuilding = () => {
    // Pass the blueprint data to the parent component
    // Always use the name from onboarding data
    const appName =
      getOnboardingProgress.data?.data?.name || blueprint?.appName || '';

    const blueprintData: Record<string, string> = blueprint
      ? {
          name: appName,
          features: JSON.stringify(blueprint.features),
        }
      : {
          name: appName,
          features: '[]',
        };

    onSubmit(blueprintData);
  };

  // Convert blueprint to lexical content for editing
  const blueprintToLexicalContent = (
    blueprintData: BlueprintData | null,
  ): SerializedEditorState => {
    if (!blueprintData) {
      return initialLexicalConfig;
    }

    // Always use the name from onboarding data
    const appName =
      getOnboardingProgress.data?.data?.name || blueprintData.appName;

    const content = `App Name: ${appName}

Core Features:

${blueprintData.features
  .map((feature) => `• ${feature.name}: ${feature.description}`)
  .join('\n')}`;

    return createLexicalFromText(content);
  };

  return (
    <div className="w-full font-inter">
      <div className="px-6">
        <div className="h-full">
          <div
            className={cn(
              'transition-all duration-300 h-full',
              isEditing && !isMobile ? 'grid grid-cols-2 gap-6' : 'block',
            )}
          >
            {/* Blueprint Card:
                  - Shows if not editing and on mobile
                  - Shows if not mobile (always, editor might be next to it)
              */}
            {(!isMobile || !isEditing) && (
              <div
                className={cn('h-[72vh]', {
                  hidden: isMobile && isEditing,
                })}
              >
                <BlueprintCard
                  blueprint={blueprint}
                  isStreaming={status === 'streaming'}
                  streamedContent={streamedContent}
                  onEdit={handleEditBlueprint}
                  onStartBuilding={handleStartBuilding}
                  isEditing={isEditing}
                />
              </div>
            )}

            {/* Editor Panel:
                  - Shows if editing (on mobile, it replaces card; on desktop, it's side-by-side)
              */}
            {isEditing && (
              <div
                className={cn('h-[72vh]', {
                  hidden: isMobile && !isEditing,
                })}
              >
                <EditorPanel
                  isOpen={isEditing}
                  initialContent={blueprintToLexicalContent(blueprint)}
                  onSave={handleSaveEdit}
                  onCancel={() => setIsEditing(false)}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
