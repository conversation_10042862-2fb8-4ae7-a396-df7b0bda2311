import { LoadingSpinner } from '@/components/loading/main';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useGetOnboardingProgress } from '../hooks/get-onboarding-progress';

const onboardingSchema = z.object({
  competitors: z.string(),
  targetCustomers: z.string(),
});

export const OnboardingStepThree = ({
  onSubmit,
  isPending,
}: {
  onSubmit: (values: Record<string, string>) => Promise<void>;
  isPending: boolean;
}) => {
  const getOnboardingProgress = useGetOnboardingProgress();
  const onboardingData = getOnboardingProgress.data?.data;
  const form = useForm<z.infer<typeof onboardingSchema>>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: {
      competitors: '',
      targetCustomers: '',
    },
  });
  const { reset } = form;

  useEffect(() => {
    reset({
      competitors: onboardingData?.competitors || '',
      targetCustomers: onboardingData?.targetCustomers || '',
    });
  }, [reset, onboardingData?.competitors, onboardingData?.targetCustomers]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-12 font-nunito"
      >
        <FormField
          control={form.control}
          name="competitors"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-white-200 font-normal">
                Know any businesses like yours out there?
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter your competitors"
                  className="bg-black-100"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="targetCustomers"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-white-200 font-normal">
                Who are your target customers?
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Textarea
                    rows={4}
                    placeholder="Enter your target customers"
                    className="bg-black-100"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          disabled={isPending}
          className="w-full font-nunito-sans"
          type="submit"
        >
          Finish
          {isPending && <LoadingSpinner />}
        </Button>
      </form>
    </Form>
  );
};
