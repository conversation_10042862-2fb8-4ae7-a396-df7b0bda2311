// hooks/useLexicalTextConverter.ts
import { $getRoot, createEditor, SerializedEditorState } from 'lexical';
import { useMemo } from 'react';

export const useLexicalTextConverter = () => {
  const editor = useMemo(() => createEditor(), []);

  const extractText = (lexicalState: SerializedEditorState): string => {
    try {
      // Parse the serialized state manually to handle lists properly
      const root = lexicalState.root;
      let text = '';

      if (root && root.children) {
        root.children.forEach((child: any) => {
          if (child.type === 'paragraph') {
            // Handle paragraph nodes
            const paragraphText =
              child.children
                ?.map((textChild: any) => textChild.text || '')
                .join('') || '';
            text += paragraphText + '\n';
          } else if (child.type === 'list') {
            // Handle list nodes
            child.children?.forEach((listItem: any) => {
              if (listItem.type === 'listitem') {
                const itemText =
                  listItem.children
                    ?.map((textChild: any) => textChild.text || '')
                    .join('') || '';
                text += `• ${itemText}\n`;
              }
            });
          }
        });
      }

      return text.trim();
    } catch (error) {
      console.error('Error extracting text from Lexical state:', error);
      // Fallback to original method
      try {
        const editorState = editor.parseEditorState(lexicalState);
        return editorState.read(() => {
          const root = $getRoot();
          return root.getTextContent();
        });
      } catch (fallbackError) {
        console.error('Fallback extraction also failed:', fallbackError);
        return '';
      }
    }
  };

  const createLexicalFromText = (plainText: string): SerializedEditorState => {
    try {
      const lines = plainText.split('\n').filter((line) => line.trim() !== '');
      const children: any[] = [];

      let currentListChildren: any[] = [];
      let inListMode = false;

      lines.forEach((line) => {
        // Check if this line is a bullet point
        const bulletMatch = line.match(/^•\s*(.+)$/);

        if (bulletMatch) {
          const textContent = bulletMatch[1];

          // Add list item to current list
          currentListChildren.push({
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: textContent,
                type: 'text',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'listitem',
            version: 1,
            value: currentListChildren.length + 1,
          });

          inListMode = true;
        } else {
          // If we were in list mode and this is not a bullet, close the list
          if (inListMode && currentListChildren.length > 0) {
            children.push({
              children: currentListChildren,
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'list',
              version: 1,
              listType: 'bullet',
              start: 1,
              tag: 'ul',
            });
            currentListChildren = [];
            inListMode = false;
          }

          // Add as regular paragraph
          children.push({
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: line,
                type: 'text',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'paragraph',
            version: 1,
          });
        }
      });

      // If we end with a list, add it
      if (inListMode && currentListChildren.length > 0) {
        children.push({
          children: currentListChildren,
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'list',
          version: 1,
          listType: 'bullet',
          start: 1,
          tag: 'ul',
        });
      }

      return {
        root: {
          children,
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      } as SerializedEditorState;
    } catch (error) {
      console.error('Error creating Lexical state from text:', error);
      // Return a basic empty state
      return {
        root: {
          children: [],
          direction: null,
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      } as SerializedEditorState;
    }
  };

  return { extractText, createLexicalFromText };
};
