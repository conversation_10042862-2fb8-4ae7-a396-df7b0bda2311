import { useGetMe } from '@/features/workspace/api/get-me';
import { useBaseStore } from '@/store/base-store';
import { useGetOnboarding } from '../api/queries';

export const useGetOnboardingProgress = () => {
  const { workspaceId: selectedWorkspaceId } = useBaseStore((state) => state);
  const me = useGetMe();
  const workspaceLength = Number(me.data?.workspaces?.length);
  const hasSingleWorkspace = workspaceLength === 1;
  const workspaceId = hasSingleWorkspace
    ? me.data?.workspaces?.[0]?.id
    : selectedWorkspaceId;

  const getOnboarding = useGetOnboarding({ id: workspaceId ?? '' });
  return getOnboarding;
};
