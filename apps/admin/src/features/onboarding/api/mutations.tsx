import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

type CreateWorkspace = {
  id: string;
  data: Record<string, any>;
};

type ProjectPlanning = {
  message: string;
  workspaceId: string;
};

const update = async (values: CreateWorkspace) => {
  const { id, data } = values;
  return api.patch(`/onboarding/${id}`, data);
};

export const useUpdateOnboarding = () => {
  return useMutation({
    mutationFn: update,
  });
};

const projectPlanning = async (values: ProjectPlanning) => {
  return api.post(`/project-planning/generate`, values);
};

export const useProjectPlanning = () => {
  return useMutation({
    mutationFn: projectPlanning,
  });
};
