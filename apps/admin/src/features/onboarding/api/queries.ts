import { api } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export interface Onboarding {
  id: string;
  workspaceId: string;
  data: Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

const getOnboarding = async ({ id }: { id?: string }): Promise<Onboarding> => {
  return api.get(`/onboarding/${id}`);
};

export const useGetOnboarding = ({ id }: { id?: string }) => {
  return useQuery({
    queryKey: ['onboarding', id],
    queryFn: () => getOnboarding({ id }),
    enabled: !!id,
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: 'always',
  });
};
