import { api } from '@/lib/api-client';

import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router';
import { VerifyResponse } from '../types/main';
import { setAuthTokens } from '../utils/main';

export type GoogleLoginSchema = {
  code: string;
};

const googleLogin = (values: GoogleLoginSchema): Promise<VerifyResponse> => {
  return api.get('/auth/google/callback', {
    params: {
      code: values.code,
    },
  });
};

export const useGoogleLogin = () => {
  const navigate = useNavigate();
  return useMutation({
    mutationFn: googleLogin,
    onSuccess: (response) => {
      setAuthTokens(response.data as unknown as VerifyResponse);
      navigate('/');
    },
  });
};
