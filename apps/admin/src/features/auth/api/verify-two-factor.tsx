import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { VerifyTwoFactorSchema } from '../pages/two-factor-verify';
import { VerifyResponse } from '../types/main';
import { setAuthTokens } from '../utils/main';

const verifyTwoFactorAuth = async (
  values: VerifyTwoFactorSchema,
): Promise<VerifyResponse> => {
  return api.post('/auth/two-factor-auth/verify', values);
};

export const useVerifyTwoFactor = () => {
  return useMutation({
    mutationFn: verifyTwoFactorAuth,
    onSuccess: (response) => {
      setAuthTokens(response);
    },
  });
};
