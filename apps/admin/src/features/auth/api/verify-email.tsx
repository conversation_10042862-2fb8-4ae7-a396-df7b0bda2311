import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { VerifySchema } from '../pages/verify';
import { VerifyResponse } from '../types/main';
import { setAuthTokens } from '../utils/main';

const verifyEmail = async (values: VerifySchema): Promise<VerifyResponse> => {
  return api.post('/auth/verify', values);
};

export const useVerifyEmail = () => {
  return useMutation({
    mutationFn: verifyEmail,
    onSuccess: (response) => {
      setAuthTokens(response);
    },
  });
};
