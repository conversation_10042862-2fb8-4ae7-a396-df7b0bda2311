import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { RecoveryTwoFactorSchema } from '../pages/two-factor-recovery';
import { VerifyResponse } from '../types/main';
import { setAuthTokens } from '../utils/main';

const recoveryTwoFactorAuth = async (
  values: RecoveryTwoFactorSchema,
): Promise<VerifyResponse> => {
  return api.post('/auth/two-factor-auth/recovery-codes', values);
};

export const useRecoveryTwoFactor = () => {
  return useMutation({
    mutationFn: recoveryTwoFactorAuth,
    onSuccess: (response) => {
      setAuthTokens(response);
    },
  });
};
