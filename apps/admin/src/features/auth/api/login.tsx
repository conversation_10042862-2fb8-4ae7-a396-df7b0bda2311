import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { LoginSchema } from '../pages/login';
import { VerifyResponse } from '../types/main';
import { setAuthTokens } from '../utils/main';

const login = async (values: LoginSchema): Promise<VerifyResponse> => {
  return api.post('/auth/login', values);
};

export const useLogin = () => {
  return useMutation({
    mutationFn: login,
    onSuccess: (response) => {
      setAuthTokens(response);
    },
  });
};
