import LogoSmall from '@/assets/logos/logo-small.svg';
import { LoadingSpinner } from '@/components/loading/main';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import { cookie } from '@/lib/cookie';
import { errorParser } from '@/lib/errors';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router';
import * as z from 'zod';
import { useResendVerification } from '../api/resend-verification';
import { useVerifyEmail } from '../api/verify-email';
import { Or } from '../components/or';
import { useVerifyTimer } from '../hooks/main';

const verifySchema = z.object({
  email: z.string(),
  code: z.string(),
});

export type VerifySchema = z.infer<typeof verifySchema>;

export default function VerifyForm() {
  const navigate = useNavigate();
  const email = cookie.get('email');
  const verifyEmail = useVerifyEmail();
  const resendVerification = useResendVerification();
  const form = useForm<VerifySchema>({
    resolver: zodResolver(verifySchema),
    defaultValues: {
      email: '',
      code: '',
    },
  });

  const { isTimerRunning, timer, setTimer, setIsTimerRunning, timerLength } =
    useVerifyTimer();

  const handleResendCode = () => {
    setTimer(timerLength);
    setIsTimerRunning(true);

    if (email) {
      resendVerification.mutate(
        { email },
        {
          onSuccess: () => {
            toast.success('Verification code sent', {
              description: 'Please check your email',
            });
          },
          onError: (error) => {
            toast.error(errorParser(error, 'message'));
          },
        },
      );
    }
  };

  const onSubmit = (values: VerifySchema) => {
    if (email) {
      verifyEmail.mutate(
        { ...values, email },
        {
          onSuccess: (response) => {
            if (response.data?.furtherAction) {
              // redirect to 2 factor auth apge
            }

            cookie.remove('email');

            navigate('/onboarding');
          },
          onError: (error) => {
            toast.error(errorParser(error, 'message'));
          },
        },
      );
    } else {
      toast.error('Please signup first');
    }
  };

  return (
    <Card
      style={{
        borderRadius: '16px',
        background: '#0D1117',
        boxShadow:
          '0px 4px 19px 0px rgba(0, 0, 0, 0.11), 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 2px 0px rgba(25, 28, 33, 0.06), 0px 0px 0px 1px rgba(0, 0, 0, 0.03)',
      }}
      className="w-[98%] md:w-[400px] max-w-[400px] md:max-w-md z-20 border-none"
    >
      <CardHeader className="space-y-2">
        <div className="flex justify-center">
          <img className="p-4" src={LogoSmall} alt="" />
        </div>

        <div className="space-y-2 text-center">
          <h1 className="text-base-plus font-semibold">Verify Your Email</h1>
          <p className="text-xs-plus text-muted-primary font-light">
            Enter the verification code sent to your email id
          </p>
          <p className="text-sm font-medium">{email}</p>
        </div>
      </CardHeader>

      <CardContent className="space-y-2">
        {/* Form Fields */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem className="flex justify-center">
                  <FormControl>
                    <InputOTP
                      maxLength={6}
                      {...field}
                      onChange={(e) => {
                        verifyEmail.reset();
                        field.onChange(e);
                      }}
                    >
                      {[1, 2, 3, 4, 5, 6].map((index) => (
                        <InputOTPGroup>
                          <InputOTPSlot
                            className={cn(
                              'w-9 h-9 sm:w-10 sm:h-10',
                              verifyEmail.isError && 'border-red-500',
                            )}
                            key={index}
                            index={index - 1}
                          />
                        </InputOTPGroup>
                      ))}
                    </InputOTP>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Or />

            {verifyEmail.isError && (
              <FormMessage>
                {errorParser(verifyEmail.error, 'message')}
              </FormMessage>
            )}

            <Button
              disabled={verifyEmail.isPending}
              className="w-full font-nunito-sans"
              type="submit"
            >
              Continue
              {verifyEmail.isPending && <LoadingSpinner />}
            </Button>
          </form>
        </Form>
      </CardContent>

      <div className="text-center text-xs mb-4">
        {isTimerRunning ? (
          <p className="text-muted-primary">
            Didn&apos;t receive a code? ({timer}s)
          </p>
        ) : (
          <Button
            variant="link"
            className="p-0 h-auto font-normal"
            onClick={handleResendCode}
          >
            Resend Code
          </Button>
        )}
      </div>

      <CardFooter className="flex flex-col space-y-4 pt-4 text-center bg-[#0D1117] rounded-b-[16px] px-0">
        <div className="text-[10px] text-muted-secondary font-gilfroy-light font-normal">
          SECURED BY{' '}
          <span className="font-gilfroy-extrabold">FULLSTACKFOX</span>
        </div>
      </CardFooter>
    </Card>
  );
}
