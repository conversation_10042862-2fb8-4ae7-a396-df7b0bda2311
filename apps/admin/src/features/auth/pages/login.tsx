import { LoadingSpinner } from '@/components/loading/main';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON>ooter,
  CardHeader,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { errorParser } from '@/lib/errors';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import { NavLink, useNavigate } from 'react-router';
import * as z from 'zod';
import { useLogin } from '../api/login';
import { GoogleLogin } from '../components/google-login';
import { Or } from '../components/or';

const loginSchema = z.object({
  email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  password: z.string().min(4, {
    message: 'Password must be at least 8 characters.',
  }),
});

export type LoginSchema = z.infer<typeof loginSchema>;

export default function LoginForm() {
  const navigate = useNavigate();
  const login = useLogin();
  const [showPassword, setShowPassword] = React.useState(false);
  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  function onSubmit(values: z.infer<typeof loginSchema>) {
    login.reset();
    login.mutate(
      { ...values },
      {
        onSuccess: (response) => {
          if (response.data?.furtherAction) {
            return navigate('/auth/two-factor-verify');
          }

          const isOnboardingComplete =
            response.data.workspace?.data?.isCompleted;

          if (!isOnboardingComplete) {
            return navigate('/onboarding');
          }

          navigate('/');
        },
        onError: (error) => {
          toast.error(errorParser(error, 'message'));
        },
      },
    );
  }

  return (
    <Card
      style={{
        borderRadius: '16px',
        background: '#0D1117',
        boxShadow:
          '0px 4px 19px 0px rgba(0, 0, 0, 0.11), 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 2px 0px rgba(25, 28, 33, 0.06), 0px 0px 0px 1px rgba(0, 0, 0, 0.03)',
      }}
      className="w-[98%] md:w-[400px] max-w-[400px] md:max-w-md z-20 border-none"
    >
      <CardHeader className="space-y-2 text-center pb-2">
        <h2 className="text-base-plus font-semibold">Sign In</h2>
        <p className="text-xs-plus text-muted-primary font-normal">
          Welcome back! Please sign in to continue
        </p>
      </CardHeader>

      <CardContent className="space-y-2 pt-2">
        <GoogleLogin />

        <Or />

        {/* Form Fields */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      {...field}
                      onChange={(e) => {
                        login.reset();
                        field.onChange(e);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Enter your password"
                        {...field}
                        onChange={(e) => {
                          login.reset();
                          field.onChange(e);
                        }}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-1/2 -translate-y-1/2"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                        <span className="sr-only">
                          {showPassword ? 'Hide password' : 'Show password'}
                        </span>
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {login.isError && (
              <FormMessage>{errorParser(login.error, 'message')}</FormMessage>
            )}

            <Button
              disabled={login.isPending}
              className="w-full font-nunito-sans"
              type="submit"
            >
              Continue
              {login.isPending && <LoadingSpinner />}
            </Button>
          </form>
        </Form>
      </CardContent>

      <CardFooter className="flex flex-col space-y-4 pt-4 text-center bg-[#0D1117] rounded-b-[16px] px-0">
        <div className="text-sm text-muted-primary font-normal font-nunito-sans py-1">
          Don’t have an account?{' '}
          <NavLink to="/auth/signup" className="text-primary hover:underline">
            Sign Up
          </NavLink>
        </div>

        <Separator />

        <div className="text-[10px] text-white font-gilfroy-light font-normal">
          SECURED BY{' '}
          <span className="font-gilfroy-extrabold">FULLSTACKFOX</span>
        </div>
      </CardFooter>
    </Card>
  );
}
