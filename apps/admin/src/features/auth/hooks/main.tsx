import React from 'react';

export const useVerifyTimer = () => {
  const timerLength = 30;
  const [isTimerRunning, setIsTimerRunning] = React.useState(true);
  const [timer, setTimer] = React.useState(timerLength);

  React.useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isTimerRunning && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
    } else if (timer === 0) {
      setIsTimerRunning(false);
    }

    return () => clearInterval(interval);
  }, [timer, isTimerRunning]);

  return {
    timerLength,
    isTimerRunning,
    timer,
    setTimer,
    setIsTimerRunning,
  };
};
