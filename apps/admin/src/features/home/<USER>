import { ColumnDef } from '@tanstack/react-table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export type Post = {
   id: string;
   thumbnail: string;
   title: string;
   type: string;
   platform: string;
   date: string;
   commentRate: string;
   hateComments: string;
   positiveComments: string;
   totalImpressions: string;
};

export const columns: ColumnDef<Post>[] = [
   {
      accessorKey: 'thumbnail',
      header: 'Post',
      cell: ({ row }) => {
         return (
            <div className='flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3'>
               <Avatar className='h-10 w-10 sm:h-12 sm:w-12'>
                  <AvatarImage src={row.getValue('thumbnail')} alt='Post thumbnail' />
                  <AvatarFallback>PT</AvatarFallback>
               </Avatar>
               <span className='font-medium text-sm sm:text-base'>{row.getValue('title')}</span>
            </div>
         );
      },
   },
   {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }) => <div className='text-sm sm:text-base'>{row.getValue('type')}</div>,
   },
   {
      accessorKey: 'platform',
      header: 'Social Media',
      cell: ({ row }) => <div className='text-sm sm:text-base'>{row.getValue('platform')}</div>,
   },
   {
      accessorKey: 'date',
      header: 'Date & Time',
      cell: ({ row }) => <div className='text-sm sm:text-base'>{row.getValue('date')}</div>,
   },
   {
      accessorKey: 'commentRate',
      header: 'Comment Rate',
      cell: ({ row }) => <div className='text-sm sm:text-base'>{row.getValue('commentRate')}</div>,
   },
   {
      accessorKey: 'hateComments',
      header: 'Hate Comments',
      cell: ({ row }) => <div className='text-sm sm:text-base'>{row.getValue('hateComments')}</div>,
   },
   {
      accessorKey: 'positiveComments',
      header: 'Positive Comments',
      cell: ({ row }) => (
         <div className='text-sm sm:text-base'>{row.getValue('positiveComments')}</div>
      ),
   },
   {
      accessorKey: 'totalImpressions',
      header: 'Total Impression',
      cell: ({ row }) => (
         <div className='text-sm sm:text-base'>{row.getValue('totalImpressions')}</div>
      ),
   },
];
