import {
  useGenerateModuleDetails,
  useGetModuleDetail,
} from '@/api/project-planning';
import { Button } from '@/components/ui/button';
import { Card, CardHeader } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ProjectBlueprint } from '@/types/project-planning-types';
import { ChevronDown, ChevronRight, MoreVertical, Plus } from 'lucide-react';
import { PageCard } from './page-card';

interface HomeContentProps {
  projectBlueprint: ProjectBlueprint;
  activeModuleId: string | null;
  onModuleSelect: (moduleId: string) => void;
  workspaceId: string | null;
}

export default function HomeContent({
  projectBlueprint,
  activeModuleId,
  onModuleSelect,
  workspaceId,
}: HomeContentProps) {
  // Get module detail for the active module
  const getModuleDetailQuery = useGetModuleDetail(
    workspaceId || undefined,
    activeModuleId || undefined,
    { enabled: !!activeModuleId && !!workspaceId },
  );

  const generateDetailsMutation = useGenerateModuleDetails();

  // Trigger module detail generation if not available
  // useEffect(() => {
  //   if (
  //     activeModuleId &&
  //     workspaceId &&
  //     !getModuleDetailQuery.data &&
  //     !getModuleDetailQuery.isLoading &&
  //     !generateDetailsMutation.isPending
  //   ) {
  //     // Check if we have a 404 error (module detail doesn't exist)
  //     const isNotFound = getModuleDetailQuery.isError;

  //     if (isNotFound) {
  //       // Trigger generation
  //       generateDetailsMutation.mutate({
  //         moduleId: activeModuleId,
  //         workspaceId,
  //       });
  //     }
  //   }
  // }, [
  //   activeModuleId,
  //   workspaceId,
  //   getModuleDetailQuery.data,
  //   getModuleDetailQuery.isLoading,
  //   getModuleDetailQuery.isError,
  //   generateDetailsMutation,
  // ]);

  // Find the active module from blueprint
  const activeModuleData = projectBlueprint?.modules?.find(
    (module) => module.id === activeModuleId,
  );

  const moduleDetail = getModuleDetailQuery.data;
  const _isLoadingModuleDetail =
    getModuleDetailQuery.isLoading || generateDetailsMutation.isPending;

  // Create story map from module detail
  const storyMap: { [key: string]: any } = {};
  if (moduleDetail?.stories) {
    moduleDetail.stories.forEach((story) => {
      storyMap[story.id] = story;
    });
  }

  // Early return if no project blueprint or modules
  if (!projectBlueprint || !projectBlueprint.modules) {
    return (
      <div className="flex flex-1 overflow-hidden items-center justify-center">
        <div className="text-gray-400">No project data available</div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 overflow-hidden">
      {/* Left Sidebar */}
      <div className="w-48 border-r border-gray-800 flex flex-col">
        <div className="flex-1 py-4">
          <nav className="space-y-1 px-2">
            {projectBlueprint.modules.map((module) => (
              <NavItem
                key={module.id}
                label={module.title}
                isActive={activeModuleId === module.id}
                onClick={() => onModuleSelect(module.id)}
              />
            ))}
          </nav>
        </div>
        <div className="p-4">
          <Button
            variant="ghost"
            className="w-full flex justify-between items-center px-4 py-2 text-gray-400 bg-[#1a1d21] rounded-full hover:bg-[#22252a]"
          >
            <span>Add Module</span>
            <div className="flex items-center justify-center h-6 w-6 rounded-full border border-gray-500">
              <Plus className="h-3 w-3" />
            </div>
          </Button>
        </div>
      </div>

      {/* Main Content with ScrollArea */}
      <div className="flex-1 flex flex-col">
        <ScrollArea className="flex-1">
          <div className="p-6">
            {activeModuleData && (
              <>
                {/* Module Header Card */}
                <Card className="mb-6 bg-[#131921] border-gray-800 rounded-lg overflow-hidden">
                  {/* <CardHeader className="flex flex-row items-center justify-between p-4 border-b border-gray-800 bg-[#1a1d21]"> */}
                  <CardHeader className="flex flex-row items-center justify-between p-4 border-b border-gray-800 bg-[#1a1d21]">
                    <div className="flex items-center gap-2">
                      <h2 className="text-xl font-semibold">
                        {activeModuleData.title}
                      </h2>
                      <ChevronDown className="h-5 w-5 text-gray-400" />
                    </div>
                    <MoreVertical className="h-5 w-5 text-gray-400" />
                  </CardHeader>
                </Card>

                {/* Page Cards */}
                <div className="grid gap-5 grid-cols-2">
                  {activeModuleData.pages &&
                    activeModuleData.pages.map((page, index) => (
                      <PageCard key={index} index={index} page={page} />
                    ))}
                </div>
              </>
            )}

            {/* No active module selected */}
            {!activeModuleData && (
              <div className="flex items-center justify-center h-64 text-gray-400">
                <p>Select a module to view its pages</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}

interface NavItemProps {
  label: string;
  isActive: boolean;
  onClick: () => void;
}

function NavItem({ label, isActive, onClick }: NavItemProps) {
  return (
    <button
      onClick={onClick}
      className={`flex items-center justify-between w-full px-3 py-2 text-sm rounded-md group transition-colors ${
        isActive
          ? 'bg-[#1a1d21] text-white'
          : 'text-gray-400 hover:text-white hover:bg-gray-800'
      }`}
      title={label} // Show full text on hover
    >
      <span className="truncate pr-2 flex-1 text-left">{label}</span>
      <ChevronRight
        className={`h-4 w-4 flex-shrink-0 ${isActive ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'} transition-opacity`}
      />
    </button>
  );
}
