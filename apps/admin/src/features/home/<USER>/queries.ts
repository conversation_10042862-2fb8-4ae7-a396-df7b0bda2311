import { api } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import * as z from 'zod';
import { ProductSchema } from '../schemas/product-planning-schema';

const getProjectPlan = async ({
  id,
}: {
  id: string;
}): Promise<{ data: z.infer<typeof ProductSchema> }> => {
  return api.get(`/project-planning/${id}`);
};

export const useGetProjectPlan = ({ id }: { id: string }) => {
  return useQuery({
    queryKey: ['project-plan', id],
    queryFn: () => getProjectPlan({ id }),
    enabled: !!id,
  });
};
