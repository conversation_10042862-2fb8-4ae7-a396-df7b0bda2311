import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { useNavigate } from 'react-router';

// Utility function to truncate text with ellipsis
const truncateText = (text: string, maxLength: number = 150): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + '...';
};

export const PageCard = ({
  page,
  index,
}: {
  page: {
    id: string;
    title: string;
    description: string;
    route: string;
  };
  index: number;
}) => {
  const navigate = useNavigate();

  return (
    <div className="flex items-center justify-center">
      <Card className="w-full max-w-2xl bg-[#1a1d21] border-gray-800 border rounded-3xl p-2 min-h-[252px] flex flex-col">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-4">
            <span className="text-white text-lg font-medium">
              {String(index + 1).padStart(2, '0')}
            </span>
            <h2 className="text-white text-lg font-medium">{page.title}</h2>
          </div>
        </CardHeader>
        <CardContent className="space-y-6 flex-1 flex flex-col">
          <p className="text-[#506178] text-sm leading-relaxed flex-1">
            {truncateText(page.description)}
          </p>

          <div className="border-t border-[#363b42]"></div>

          <div className="flex justify-center">
            <Button
              onClick={() => {
                navigate(`/chat?pageId=${page.id}`);
              }}
              className="bg-[#363b42] hover:bg-[#1f242b] text-white px-8  rounded-lg border w-[200px] border-gray-800 py-4"
              variant="secondary"
            >
              Create
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
