import {
  useGetWorkspace,
  Workspace,
} from '@/components/side-panel/api/get-workspace';
import { format } from 'date-fns';

// Transform workspace data to project UI format
export const transformWorkspaceToProject = (workspace: Workspace) => {
  // Generate a consistent seed based on workspace ID for UI patterns
  const seed = workspace.id
    .split('')
    .reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0);

  // Format last edit date using date-fns
  const lastEditDate = new Date(workspace.updated_at);
  const lastEdit = `Last edit on ${format(lastEditDate, 'MMM dd')}`;

  return {
    id: workspace.id, // Convert to number to match existing interface
    title: workspace.name,
    lastEdit,
    seed,
  };
};

// Re-export the hook with same functionality
export const useGetProjects = useGetWorkspace;
