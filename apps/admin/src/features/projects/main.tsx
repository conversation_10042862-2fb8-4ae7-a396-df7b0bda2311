import { CreateProjectModal } from '@/components/side-panel/components/create-project-modal';
import { Card, CardContent } from '@/components/ui/card';
import { errorParser } from '@/lib/errors';
import { toast } from '@/lib/toast';
import { useBaseStore } from '@/store/base-store';
import { clearCurrentGitWorkspace } from '@/utils/git-utils';
import { useQueryClient } from '@tanstack/react-query';
import { Plus } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router';
import { CreateProjectPayload, useCreateProject } from './api/create-project';
import {
  transformWorkspaceToProject,
  useGetProjects,
} from './api/get-projects';

interface Project {
  id: string;
  title: string;
  lastEdit: string;
  seed: number;
}

interface ProjectFormData {
  name: string;
}

export default function Component() {
  const { data: workspaces, isLoading, error } = useGetProjects();
  const createProject = useCreateProject();
  const { workspaceId, setWorkspaceId } = useBaseStore((state) => state);
  const queryClient = useQueryClient();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const navigate = useNavigate();

  // Transform workspaces to projects for UI
  const projects: Project[] = useMemo(() => {
    return workspaces?.map(transformWorkspaceToProject) || [];
  }, [workspaces]);

  // Set first project as selected if none is selected
  useEffect(() => {
    if (projects.length > 0 && !workspaceId) {
      const firstProject = projects[0];
      setWorkspaceId(firstProject.id);
    }
  }, [projects, workspaceId, setWorkspaceId]);

  const handleCreateProject = (data: ProjectFormData) => {
    const payload: CreateProjectPayload = { name: data.name };

    createProject.mutate(payload, {
      onSuccess: (newWorkspace) => {
        // Invalidate and refetch projects
        queryClient.invalidateQueries({ queryKey: ['/workspace'] });

        // Set new project as selected
        setWorkspaceId(newWorkspace.id);

        // Close modal and show success
        setIsCreateModalOpen(false);

        navigate('/onboarding');
        toast.success('Project created successfully');
      },
      onError: (error) => {
        toast.error(errorParser(error));
      },
    });
  };

  const handleCreateNewProject = () => {
    setIsCreateModalOpen(true);
  };

  const handleProjectSelect = ({
    currentProjectId,
    newProjectId,
  }: {
    currentProjectId: string;
    newProjectId: string;
  }) => {
    /**
     * ALWAYS CLEAR THE CURRENT WORKSPACE ON SWITCH
     * This is so that there only exists one db at a time.
     * Easier to manage and debug
     */
    clearCurrentGitWorkspace(currentProjectId);
    setWorkspaceId(newProjectId);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="ml-16 p-8 flex items-center justify-center">
        <div className="text-[#6b7280]">Loading projects...</div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="ml-16 p-8 flex items-center justify-center">
        <div className="text-red-400">
          Error loading projects: {errorParser(error)}
        </div>
      </div>
    );
  }

  // Generate a pseudo-random number between min and max based on seed
  const seededRandom = (seed: number, min: number, max: number) => {
    const x = Math.sin(seed) * 10000;
    const result = x - Math.floor(x);
    return min + result * (max - min);
  };

  // Generate abstract pattern for project cards
  const getProjectPattern = (seed: number) => {
    // Generate colors based on seed - always include orange tones
    const hue1 = 25; // Orange base
    const hue2 = Math.floor(seededRandom(seed + 100, 15, 35)); // Orange variations

    return (
      <div className="relative w-full h-24 bg-[#1a1f2e] rounded-lg overflow-hidden">
        {/* Abstract pattern background with orange tint */}
        <div className="absolute inset-0 opacity-30">
          <div
            className="absolute inset-0 blur-sm"
            style={{
              background: `radial-gradient(circle at ${seededRandom(seed, 20, 80)}% ${seededRandom(seed + 50, 20, 80)}%, 
                          hsl(${hue1}, 80%, 50%) 0%, 
                          hsl(${hue2}, 70%, 45%) 100%)`,
            }}
          />
        </div>

        {/* Single abstract pattern */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full h-full p-3">
            <div className="grid grid-cols-3 grid-rows-2 gap-1 h-full">
              <div className="bg-white/10 rounded"></div>
              <div className="bg-white/5 rounded"></div>
              <div className="bg-white/10 rounded"></div>
              <div className="col-span-2 bg-white/5 rounded"></div>
              <div className="bg-white/10 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Main Content */}
      <div className="p-8 py-5">
        <h2 className="mb-6 text-lg">Projects</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {/* Project Cards */}
          {projects.map((project) => (
            <Card
              key={project.id}
              onClick={() => {
                if (workspaceId) {
                  handleProjectSelect({
                    currentProjectId: workspaceId,
                    newProjectId: project.id,
                  });
                } else {
                  toast.error(
                    'Current workspace ID is missing. Please reload the page.',
                  );
                }
              }}
              className={`bg-[#10151c] border-2 transition-all duration-200 cursor-pointer hover:scale-[1.02] ${workspaceId === project.id ? 'border-[#ff712f] shadow-lg shadow-[#ff712f]/10' : 'border-[#1d2127] hover:border-[#363b42]'}`}
            >
              <CardContent className="p-4">
                <h3 className="text-white font-medium mb-4">{project.title}</h3>

                {/* Project Preview with simplified orange-tinted pattern */}
                {getProjectPattern(project.seed)}

                {/* Footer */}
                <div className="text-[#6b7280] text-xs mt-4">
                  {project.lastEdit}
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Create New Project Card */}
          <Card
            onClick={handleCreateNewProject}
            className="bg-[#10151c] border-2 border-dashed border-[#1d2127] hover:border-[#363b42] transition-all duration-200 cursor-pointer hover:scale-[1.02]"
          >
            <CardContent className="p-4 h-full flex flex-col justify-center items-center text-center">
              <div className="w-12 h-12 border-2 border-dashed border-[#363b42] rounded-lg flex items-center justify-center mb-3">
                <Plus className="w-5 h-5 text-[#6b7280]" />
              </div>
              <span className="text-[#6b7280] text-sm font-medium">
                Create new Project
              </span>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Create Project Modal Component */}
      <CreateProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateProject}
        isLoading={createProject.isPending}
      />
    </>
  );
}
