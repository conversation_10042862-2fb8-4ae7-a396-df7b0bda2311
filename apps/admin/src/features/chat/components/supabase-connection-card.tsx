import { Button } from '@/components/ui/button';
import { useBaseStore } from '@/store/base-store';
import { Database, X } from 'lucide-react';
import { useNavigate } from 'react-router';

export const SupabaseConnectionCard = () => {
  const { setShowSupabaseCard } = useBaseStore((state) => state);
  const navigate = useNavigate();

  const handleConnectNow = () => {
    // Navigate to the integrations page with highlighted parameter
    navigate('/integrations?highlighted=supabase');
  };

  const handleDismiss = () => {
    setShowSupabaseCard(false);
  };

  return (
    <div className="mb-4 mx-auto w-full max-w-2xl">
      <div className="bg-gradient-to-r from-green-900/20 to-emerald-900/20 border border-green-700/30 rounded-2xl p-4 relative">
        {/* Dismiss button */}
        <button
          onClick={handleDismiss}
          className="absolute top-3 right-3 text-gray-400 hover:text-white transition-colors"
          aria-label="Dismiss"
        >
          <X className="h-4 w-4" />
        </button>

        <div className="flex items-start gap-4">
          {/* Icon */}
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-600/20 border border-green-500/30 flex items-center justify-center mt-1">
            <Database className="w-5 h-5 text-green-400" />
          </div>

          {/* Content */}
          <div className="flex-1 pr-6">
            <h3 className="text-white font-medium text-base mb-2">
              Connect to Supabase
            </h3>
            <p className="text-gray-300 text-sm mb-4 leading-relaxed">
              Your project has been configured to use Supabase. Please connect
              your Supabase account.
            </p>

            <Button
              onClick={handleConnectNow}
              size="sm"
              className="bg-green-600 hover:bg-green-700 text-white border-0 px-4 py-2 text-sm font-medium"
            >
              Connect Now
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
