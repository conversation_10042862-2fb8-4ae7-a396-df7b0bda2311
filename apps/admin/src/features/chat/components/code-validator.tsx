// components/CodeValidator.tsx
import React from 'react';
import { useValidateFiles } from '../api/validation';
import type { ValidationError, ValidationOptions } from '../types/validation';

interface CodeValidatorProps {
  generatedFiles: Record<string, string>;
  validationOptions?: ValidationOptions;
}

interface ValidationResultsProps {
  isValid: boolean;
  errors: ValidationError[];
}

const ValidationResults: React.FC<ValidationResultsProps> = ({
  isValid,
  errors,
}) => (
  <div className="validation-results">
    <div className="validation-summary">
      <h3>{isValid ? '✅ Code is valid!' : '❌ Validation failed'}</h3>
      <p>Validation is {isValid}</p>
    </div>

    {errors.length > 0 && (
      <div className="errors">
        <h4>Errors ({errors.length})</h4>
        {errors.map((error: ValidationError, index: number) => (
          <div key={index} className="error-item">
            <strong>
              {error.source}:{error.line}:{error.column}
            </strong>
            <p>{error.message}</p>
            <small>Source: {error.source}</small>
          </div>
        ))}
      </div>
    )}
  </div>
);

const CodeValidator: React.FC<CodeValidatorProps> = ({
  generatedFiles,
  validationOptions,
}) => {
  const validateFiles = useValidateFiles();
  const isValidating = validateFiles.isPending;
  const validationResults = validateFiles.data;
  const error = validateFiles.error?.message || null;

  const handleValidate = async (): Promise<void> => {
    validateFiles.mutate(
      {
        files: generatedFiles,
        options: validationOptions,
      },
      {
        onError: (err) => {
          console.error('Validation error:', err);
        },
      },
    );
  };

  return (
    <div className="code-validator">
      <div className="validator-controls">
        <button
          onClick={handleValidate}
          disabled={
            isValidating ||
            !generatedFiles ||
            Object.keys(generatedFiles).length === 0
          }
        >
          {isValidating ? 'Validating...' : 'Validate Code'}
        </button>
      </div>

      {error && (
        <div className="error-message">
          <p>Validation error: {error}</p>
        </div>
      )}

      {validationResults && (
        <ValidationResults
          isValid={validationResults.isValid}
          errors={validationResults.errors}
        />
      )}
    </div>
  );
};

export default CodeValidator;
