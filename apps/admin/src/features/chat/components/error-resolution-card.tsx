import { Button } from '@/components/ui/button';
import { useBaseStore } from '@/store/base-store';
import { AlertTriangle, Wrench, X } from 'lucide-react';

interface ErrorResolutionCardProps {
  error: string;
  onResolveError: () => void;
}

export const ErrorResolutionCard = ({
  error,
  onResolveError,
}: ErrorResolutionCardProps) => {
  const { setShowErrorCard } = useBaseStore((state) => state);

  const handleResolveError = () => {
    onResolveError();
    setShowErrorCard(false); // Hide the card after resolving
  };

  const handleDismiss = () => {
    setShowErrorCard(false);
  };

  return (
    <div className="mb-4 mx-auto w-full max-w-2xl">
      <div className="bg-gradient-to-r from-red-900/20 to-orange-900/20 border border-red-700/30 rounded-2xl p-4 relative">
        {/* Dismiss button */}
        <button
          onClick={handleDismiss}
          className="absolute top-3 right-3 text-gray-400 hover:text-white transition-colors"
          aria-label="Dismiss"
        >
          <X className="h-4 w-4" />
        </button>

        <div className="flex items-start gap-4">
          {/* Icon */}
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-red-600/20 border border-red-500/30 flex items-center justify-center mt-1">
            <AlertTriangle className="w-5 h-5 text-red-400" />
          </div>

          {/* Content */}
          <div className="flex-1 pr-6">
            <h3 className="text-white font-medium text-base mb-2">
              Build Error Detected
            </h3>
            <p className="text-gray-300 text-sm mb-3 leading-relaxed">
              There was an error during the build process. Let me help you fix
              it.
            </p>

            {/* Error details */}
            <div className="bg-red-950/30 border border-red-800/30 rounded-lg p-3 mb-4">
              <div className="text-red-200 text-xs font-mono leading-relaxed max-h-32 overflow-y-auto">
                {error}
              </div>
            </div>

            <Button
              onClick={handleResolveError}
              size="sm"
              className="bg-red-600 hover:bg-red-700 text-white border-0 px-4 py-2 text-sm font-medium flex items-center gap-2"
            >
              <Wrench className="w-4 h-4" />
              Fix This Error
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
