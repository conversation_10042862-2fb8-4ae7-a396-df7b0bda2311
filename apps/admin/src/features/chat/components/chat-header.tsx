'use client';

import GithubLogo from '@/assets/logos/github.svg';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ChevronDown, Lock } from 'lucide-react';
import { useState } from 'react';
import { Link } from 'react-router';
import { useGetMe } from '../../workspace/api/get-me';
import { GitHubModal } from './GitHubModal';

export const ChatHeader = () => {
  const [isGitHubModalOpen, setIsGitHubModalOpen] = useState(false);
  const me = useGetMe();
  const workspaceLength = Number(me.data?.workspaces?.length) - 1;
  const workspaceName =
    me.data?.workspaces?.[workspaceLength]?.name || 'My Project';

  return (
    <header className="flex items-center justify-between px-6 py-5  text-white">
      <div className="flex items-center gap-2">
        <Link to="/">
          <ArrowLeft className="w-4 h-4" />
        </Link>

        <div className="flex items-center text-sm gap-1.5 font-medium">
          {workspaceName}
          <ChevronDown className="w-4 h-4" />
        </div>

        <Badge
          variant="outline"
          className="flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium border-gray-700 bg-transparent text-white"
        >
          <Lock className="w-3 h-3" />
          Private
        </Badge>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="icon"
          className="text-gray-300 hover:text-white hover:bg-gray-800"
          onClick={() => setIsGitHubModalOpen(true)}
        >
          <img src={GithubLogo} alt="Github" className="" />
        </Button>
        <button
          className="bg-[#DF5802] hover:bg-orange-600 text-white border-none p-2 py-1 rounded-md text-sm font-semibold"
          onClick={() => setIsGitHubModalOpen(true)}
        >
          Publish
        </button>
      </div>

      <GitHubModal
        isOpen={isGitHubModalOpen}
        onClose={() => setIsGitHubModalOpen(false)}
      />
    </header>
  );
};
