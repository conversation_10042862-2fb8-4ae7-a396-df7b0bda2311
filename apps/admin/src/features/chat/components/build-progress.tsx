import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Progress } from '@/components/ui/progress';
import {
  CheckCircle,
  ChevronDown,
  ChevronRight,
  Loader2,
  XCircle,
} from 'lucide-react';
import { useEffect, useState } from 'react';

export type BuildStatus = 'processing' | 'completed' | 'failed';

export type BuildProgressMessage =
  | {
      type: 'build-status';
      data: {
        jobId: string;
        status: BuildStatus;
        message: string;
        progress: number;
        error?: string;
        logs?: string;
      };
    }
  | {
      type: 'connected';
      message: string;
    };

interface BuildProgressProps {
  messages: BuildProgressMessage[];
  error: string | undefined;
  setError: React.Dispatch<React.SetStateAction<string | undefined>>;
  logs: string | undefined;
  setLogs: React.Dispatch<React.SetStateAction<string | undefined>>;
  showLogs: boolean;
  setShowLogs: React.Dispatch<React.SetStateAction<boolean>>;
}

export const BuildProgress = ({
  messages,
  error,
  setError,
  logs,
  setLogs,
  showLogs,
  setShowLogs,
}: BuildProgressProps) => {
  const [currentProgress, setCurrentProgress] = useState(0);
  const [currentStatus, setCurrentStatus] = useState<BuildStatus>('processing');
  const [currentMessage, setCurrentMessage] = useState('Initializing build...');

  useEffect(() => {
    if (messages.length === 0) return;

    // Get the latest message
    const latestMessage = messages[messages.length - 1];

    if (latestMessage.type === 'build-status') {
      setCurrentProgress(latestMessage.data.progress || 0);
      setCurrentStatus(latestMessage.data.status);
      setCurrentMessage(latestMessage.data.message);

      if (latestMessage.data.error) {
        setError(latestMessage.data.error);
      } else {
        setError(undefined);
      }

      if (latestMessage.data.logs) {
        setLogs(latestMessage.data.logs);
      } else {
        setLogs(undefined);
      }
    }
  }, [messages, setError, setLogs]);

  return (
    <div className="w-full h-full flex flex-col items-center justify-center p-6">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {currentStatus === 'processing' && (
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
            )}
            {currentStatus === 'completed' && (
              <CheckCircle className="h-5 w-5 text-green-500" />
            )}
            {currentStatus === 'failed' && (
              <XCircle className="h-5 w-5 text-red-500" />
            )}
            Build{' '}
            {currentStatus === 'processing' ? 'in progress' : currentStatus}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{currentMessage}</span>
              <span>{currentProgress}%</span>
            </div>
            <Progress
              value={currentProgress}
              max={100}
              className="h-2"
              indicatorClassName={
                currentStatus === 'completed'
                  ? 'bg-green-500'
                  : currentStatus === 'failed'
                    ? 'bg-red-500'
                    : undefined
              }
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertTitle>Build Failed</AlertTitle>
              <AlertDescription className="space-y-3">
                <div>{error}</div>
                {logs && (
                  <Collapsible open={showLogs} onOpenChange={setShowLogs}>
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-between"
                      >
                        {showLogs ? 'Hide' : 'Show'} Build Logs
                        {showLogs ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-2">
                      <div className="rounded-md bg-gray-900 p-3 text-xs text-gray-100 font-mono max-h-64 overflow-y-auto whitespace-pre-wrap">
                        {logs}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
