import { api } from '@/lib/api-client';
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  ProjectValidationResult,
  ValidationOptions,
} from '../types/validation';

export interface ValidateFilesRequest {
  files: Record<string, string>;
  options?: ValidationOptions;
}

export interface ValidationHealthResponse {
  status: string;
  timestamp: string;
  service: string;
  cache: {
    size: number;
    maxSize: number;
    hits: number;
    misses: number;
    hitRate: string;
  };
}

// API functions
export const validateFiles = async (
  request: ValidateFilesRequest,
): Promise<ProjectValidationResult> => {
  return await api.post('/validation/files', request);
};

export const getValidationHealth =
  async (): Promise<ValidationHealthResponse> => {
    return await api.get('/validation/health');
  };

export const clearValidationCache = async (): Promise<{
  message: string;
  timestamp: string;
}> => {
  return await api.delete('/validation/cache');
};

// TanStack Query hooks
export const useValidationHealth = () => {
  return useQuery({
    queryKey: ['validation-health'],
    queryFn: getValidationHealth,
    refetchInterval: 30000, // Refetch every 30 seconds
    retry: 3,
  });
};

export const useValidateFiles = () => {
  return useMutation<ProjectValidationResult, Error, ValidateFilesRequest>({
    mutationFn: validateFiles,
    mutationKey: ['validate-files'],
  });
};

export const useClearValidationCache = () => {
  return useMutation({
    mutationFn: clearValidationCache,
    mutationKey: ['clear-validation-cache'],
  });
};

// Utility hook to check if validation service is available
export const useValidationServiceStatus = () => {
  const { data: health, isError, isLoading } = useValidationHealth();

  return {
    isAvailable:
      !isError && (health?.status === 'ok' || health?.status === 'healthy'),
    isLoading,
    health,
  };
};
