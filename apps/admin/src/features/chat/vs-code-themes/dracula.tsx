export const draculaTheme = {
  base: 'vs-dark' as const,
  inherit: true,
  rules: [
    {
      background: '0d1117',
      token: '',
    },
    {
      foreground: '6e7681',
      token: 'comment',
    },
    {
      foreground: 'e5c07b',
      token: 'string',
    },
    {
      foreground: 'd19a66',
      token: 'constant.numeric',
    },
    {
      foreground: '79c0ff',
      token: 'constant.language',
    },
    {
      foreground: '79c0ff',
      token: 'constant.character',
    },
    {
      foreground: '79c0ff',
      token: 'constant.other',
    },
    {
      foreground: 'ff7b72',
      token: 'variable.other.readwrite.instance',
    },
    {
      foreground: 'c586c0',
      token: 'constant.character.escaped',
    },
    {
      foreground: 'c586c0',
      token: 'constant.character.escape',
    },
    {
      foreground: 'c586c0',
      token: 'string source',
    },
    {
      foreground: 'c586c0',
      token: 'string source.ruby',
    },
    {
      foreground: 'c586c0',
      token: 'keyword',
    },
    {
      foreground: 'c586c0',
      token: 'storage',
    },
    {
      foreground: '79c0ff',
      fontStyle: 'italic',
      token: 'storage.type',
    },
    {
      foreground: '7ee787',
      token: 'entity.name.class',
    },
    {
      foreground: '7ee787',
      fontStyle: 'italic',
      token: 'entity.other.inherited-class',
    },
    {
      foreground: 'd2a8ff',
      token: 'entity.name.function',
    },
    {
      foreground: 'ffa657',
      fontStyle: 'italic',
      token: 'variable.parameter',
    },
    // HTML tag rules
    {
      foreground: '7ee787',
      token: 'entity.name.tag',
    },
    {
      foreground: '7ee787',
      token: 'punctuation.definition.tag',
    },
    // JSX specific rules
    {
      foreground: '7ee787',
      token: 'punctuation.definition.tag.jsx',
    },
    {
      foreground: '7ee787',
      token: 'punctuation.section.embedded',
    },
    {
      foreground: '7ee787',
      token: 'meta.jsx.children.tsx',
    },
    {
      foreground: '7ee787',
      token: 'meta.tag.jsx',
    },
    {
      foreground: '7ee787',
      token: 'meta.tag.js',
    },
    {
      foreground: '7ee787',
      token: 'meta.tag.tsx',
    },
    // Specific JSX tag tokens
    {
      foreground: '7ee787',
      token: 'entity.name.tag.jsx',
    },
    {
      foreground: '7ee787',
      token: 'entity.name.tag.js',
    },
    {
      foreground: '7ee787',
      token: 'entity.name.tag.tsx',
    },
    // Additional JSX tokens
    {
      foreground: '7ee787',
      token: 'punctuation.section.embedded.begin.jsx',
    },
    {
      foreground: '7ee787',
      token: 'punctuation.section.embedded.end.jsx',
    },
    {
      foreground: '7ee787',
      token: 'punctuation.section.embedded.begin.js',
    },
    {
      foreground: '7ee787',
      token: 'punctuation.section.embedded.end.js',
    },
    {
      foreground: '7ee787',
      token: 'punctuation.section.embedded.begin.tsx',
    },
    {
      foreground: '7ee787',
      token: 'punctuation.section.embedded.end.tsx',
    },
    // This is a catch-all for JSX tags
    {
      foreground: '7ee787',
      token: 'punctuation.definition.tag.begin',
    },
    {
      foreground: '7ee787',
      token: 'punctuation.definition.tag.end',
    },
    // End of JSX specific rules
    {
      foreground: '79c0ff',
      token: 'entity.other.attribute-name',
    },
    {
      foreground: '79c0ff',
      token: 'support.function',
    },
    {
      foreground: '79c0ff',
      token: 'support.constant',
    },
    {
      foreground: '79c0ff',
      fontStyle: 'italic',
      token: 'support.type',
    },
    {
      foreground: '79c0ff',
      fontStyle: 'italic',
      token: 'support.class',
    },
    {
      foreground: 'f0f6fc',
      background: 'f85149',
      token: 'invalid',
    },
    {
      foreground: 'f0f6fc',
      background: '8957e5',
      token: 'invalid.deprecated',
    },
    {
      foreground: '79c0ff',
      token: 'meta.structure.dictionary.json string.quoted.double.json',
    },
    {
      foreground: '6e7681',
      token: 'meta.diff',
    },
    {
      foreground: '6e7681',
      token: 'meta.diff.header',
    },
    {
      foreground: 'ff7b72',
      token: 'markup.deleted',
    },
    {
      foreground: '7ee787',
      token: 'markup.inserted',
    },
    {
      foreground: 'e5c07b',
      token: 'markup.changed',
    },
    {
      foreground: 'd2a8ff',
      token: 'constant.numeric.line-number.find-in-files - match',
    },
    {
      foreground: 'e5c07b',
      token: 'entity.name.filename',
    },
    {
      foreground: 'ff7b72',
      token: 'message.error',
    },
    {
      foreground: 'f0f6fc',
      token:
        'punctuation.definition.string.begin.json - meta.structure.dictionary.value.json',
    },
    {
      foreground: 'f0f6fc',
      token:
        'punctuation.definition.string.end.json - meta.structure.dictionary.value.json',
    },
    {
      foreground: '79c0ff',
      token: 'meta.structure.dictionary.json string.quoted.double.json',
    },
    {
      foreground: 'e5c07b',
      token: 'meta.structure.dictionary.value.json string.quoted.double.json',
    },
    {
      foreground: '7ee787',
      token:
        'meta meta meta meta meta meta meta.structure.dictionary.value string',
    },
    {
      foreground: 'ffa657',
      token: 'meta meta meta meta meta meta.structure.dictionary.value string',
    },
    {
      foreground: 'c586c0',
      token: 'meta meta meta meta meta.structure.dictionary.value string',
    },
    {
      foreground: 'd2a8ff',
      token: 'meta meta meta meta.structure.dictionary.value string',
    },
    {
      foreground: '7ee787',
      token: 'meta meta meta.structure.dictionary.value string',
    },
    {
      foreground: 'ffa657',
      token: 'meta meta.structure.dictionary.value string',
    },
    {
      foreground: 'c586c0',
      token: 'keyword.operator',
    },
    {
      foreground: 'ff7b72',
      token: 'keyword.operator.new',
    },
    {
      foreground: 'ffa657',
      token: 'constant.language.boolean',
    },
    {
      foreground: 'e5c07b',
      token: 'string.quoted.single',
    },
    {
      foreground: 'e5c07b',
      token: 'string.quoted.double',
    },
    {
      foreground: '79c0ff',
      token: 'variable.other.property',
    },
    {
      foreground: '79c0ff',
      token: 'variable.other.object.property',
    },
    {
      foreground: 'ffa657',
      token: 'string.template variable',
    },
  ],
  colors: {
    'editor.foreground': '#f0f6fc',
    'editor.background': '#0d1117',
    'editor.selectionBackground': '#264f78',
    'editor.lineHighlightBackground': '#161b22',
    'editorCursor.foreground': '#c9d1d9',
    'editorWhitespace.foreground': '#484f58',
    'editorIndentGuide.activeBackground': '#484f58',
    'editor.selectionHighlightBorder': '#264f78',
  },
};

// 'editor.background': '#0d1116',
