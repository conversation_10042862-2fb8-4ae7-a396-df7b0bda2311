import type { FrontendStoryResponse } from '@/types/project-planning-types';
import type { Message } from '@ai-sdk/react';
import type { UseQueryResult } from '@tanstack/react-query';
import { useEffect, useRef } from 'react';
import {
  formatStoriesMessage,
  isStoriesDataReady,
} from '../utils/format-stories-message';

interface UseAutoStoryStartOptions {
  chatHistory: UseQueryResult<Message[], Error>;
  storiesData: UseQueryResult<FrontendStoryResponse, Error>;
  pageId: string | null;
  isEnabled?: boolean;
  append: (message: { role: 'user' | 'assistant'; content: string }) => void;
  messages: Message[];
  status: 'error' | 'submitted' | 'streaming' | 'ready';
}

/**
 * Custom hook to manage automatic story initialization in chat using append method
 *
 * Triggers auto-start when:
 * - Chat history is empty (successful load with 0 messages)
 * - Stories are successfully loaded with data
 * - Page ID is available
 * - Feature is enabled
 * - Haven't auto-started for this page yet
 */
export function useAutoStoryStart({
  chatHistory,
  storiesData,
  pageId,
  isEnabled = true,
  append,
  messages,
  status,
}: UseAutoStoryStartOptions): void {
  // Track auto-start state - similar to onboarding implementation
  const hasGeneratedRef = useRef(false);
  const lastPageIdRef = useRef<string | null>(null);

  // Reset auto-start when page changes
  useEffect(() => {
    if (lastPageIdRef.current !== pageId) {
      lastPageIdRef.current = pageId;
      hasGeneratedRef.current = false;
    }
  }, [pageId]);

  // Auto-start logic - similar to onboarding useEffect pattern
  useEffect(() => {
    // Prevent generation if already generated, streaming, or messages exist
    if (
      hasGeneratedRef.current ||
      status === 'streaming' ||
      messages.length > 0
    ) {
      return;
    }

    // Check if all conditions are met for auto-start
    const shouldAutoStart = !!(
      isEnabled &&
      pageId &&
      // Chat history conditions
      chatHistory.isSuccess &&
      Array.isArray(chatHistory.data) &&
      chatHistory.data.length === 0 &&
      // Stories data conditions
      storiesData.isSuccess &&
      storiesData.data &&
      isStoriesDataReady(storiesData.data)
    );

    if (!shouldAutoStart) {
      return;
    }

    // Set the ref to true to indicate generation has started
    hasGeneratedRef.current = true;

    try {
      // Format stories message
      const autoStartMessage = formatStoriesMessage(storiesData.data, pageId);

      // Use append to send the stories message
      append({
        role: 'user',
        content: autoStartMessage,
      });
    } catch (error) {
      console.error('Failed to format stories message:', error);
      // Reset on error so user can try again
      hasGeneratedRef.current = false;
    }
  }, [
    isEnabled,
    pageId,
    chatHistory.isSuccess,
    chatHistory.data,
    storiesData.isSuccess,
    storiesData.data,
    status,
    messages.length,
    append,
  ]);
}
