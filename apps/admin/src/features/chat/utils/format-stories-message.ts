import type { FrontendStoryResponse } from '@/types/project-planning-types';

/**
 * Formats stories data into a structured chat message for AI consumption
 */
export function formatStoriesMessage(
  storiesData: FrontendStoryResponse,
  pageId: string,
): string {
  const _hasPage = pageId;

  return `This is the start of the page. Create the page based on the blueprint provided and user stories.
  
<fullstackfoxUserStories>
  List of stories to implement - ${JSON.stringify(storiesData.stories, null, 2)}
</fullstackfoxUserStories>

<fullstackfoxPageDetails>
Page Details - ${JSON.stringify(storiesData.page, null, 2)}
</fullstackfoxPageDetails>
`;
}

/**
 * Validates that stories data is ready for formatting
 */
export function isStoriesDataReady(
  storiesData: FrontendStoryResponse | undefined,
): boolean {
  // Check for stories and either "page" or "page" field
  const data = storiesData as FrontendStoryResponse | undefined;
  const hasPageData = !!(data?.page || data?.page);
  const hasStories = !!(data?.stories && data.stories.length > 0);

  return hasPageData && hasStories;
}
