/**
 * Cleans code blocks from AI output by removing markdown code block syntax
 * and language identifiers
 */
export function cleanCodeBlocks(content: string): string {
  // Remove markdown code block syntax and language identifiers
  // This regex matches code blocks with language identifiers like ```html, ```css, etc.
  return content.replace(
    /```(?:[\w-]+)?\s*([\s\S]*?)```/g,
    (_, codeContent) => {
      return codeContent.trim();
    },
  );
}

/**
 * Beautifies code based on file extension
 * You could integrate with libraries like prettier for more advanced formatting
 */
export function beautifyCode(code: string, _filePath: string): string {
  // Simple indentation fix - you might want to use a proper beautifier library
  // like prettier for more advanced formatting
  const lines = code.split('\n');
  const formattedLines = [];
  let indentLevel = 0;

  // Very basic formatting - you'd want to use a proper library for production
  for (const line of lines) {
    const trimmedLine = line.trim();

    // Decrease indent for closing tags/braces
    if (/^[}\])]|<\//.test(trimmedLine)) {
      indentLevel = Math.max(0, indentLevel - 1);
    }

    // Add the line with proper indentation
    formattedLines.push('  '.repeat(indentLevel) + trimmedLine);

    // Increase indent for opening tags/braces
    if (
      /[{[(]$|<[^/][^>]*>$/.test(trimmedLine) &&
      !/<(?:img|br|hr|input)[^>]*>$/.test(trimmedLine)
    ) {
      indentLevel++;
    }
  }

  return formattedLines.join('\n');
}
