export interface ValidationError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning';
  source: string;
  code?: string | number;
  ruleId?: string;
  filePath?: string;
}

export interface ProjectValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

export interface ValidationOptions {
  enableTypeScript?: boolean;
  enableESLint?: boolean;
  enableNextJS?: boolean;
  enableSyntax?: boolean;
}
