import { LoadingSpinner } from '@/components/loading/main';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useQueryClient } from '@tanstack/react-query';
import { useDisableTwoFactorAuth } from '../api/disable-two-factor';

export const DisableTwoAuthModal = ({
  isOpen,
  setIsOpen,
}: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}) => {
  const disableTwoFactorAuth = useDisableTwoFactorAuth();
  const queryClient = useQueryClient();

  const onSubmit = () => {
    disableTwoFactorAuth.mutate(undefined, {
      onSuccess: () => {
        setIsOpen(false);
        queryClient.invalidateQueries({ queryKey: ['/auth/me'] });
      },
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Disable Two Factor Auth</DialogTitle>
          <DialogDescription>
            Are you sure you want to disable your two factor auth? This action
            cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-end space-x-2 mt-4">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button
            disabled={disableTwoFactorAuth.isPending}
            variant="destructive"
            onClick={onSubmit}
          >
            Disable Two Factor
            {disableTwoFactorAuth.isPending && <LoadingSpinner />}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
