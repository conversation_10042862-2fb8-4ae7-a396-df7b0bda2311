import { useUploadFile } from '@/api/post-file';
import { LoadingSpinner } from '@/components/loading/main';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { errorParser } from '@/lib/errors';
import { toast } from '@/lib/toast';

export const ProfilePictureModal = ({
  isImagePreviewOpen,
  setIsImagePreviewOpen,
  fileInputRef,
  selectedImage,
  setSelectedImage,
}: {
  isImagePreviewOpen: boolean;
  setIsImagePreviewOpen: (isOpen: boolean) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  selectedImage: string | null;
  setSelectedImage: (imageUrl: string | null) => void;
}) => {
  const uploadFile = useUploadFile();
  const handleUpdateImage = () => {
    const formData = new FormData();
    formData.append('file', fileInputRef.current?.files?.[0] || '');
    uploadFile.mutate(
      {
        file: formData,
      },
      {
        onSuccess: () => {
          toast.success('profile pic updated');
          setIsImagePreviewOpen(false);
        },
        onError: (error) => {
          toast.error(errorParser(error, 'message'));
        },
      },
    );
  };

  const handleCloseImagePreview = () => {
    setIsImagePreviewOpen(false);
    setSelectedImage(null);
  };

  return (
    <Dialog open={isImagePreviewOpen} onOpenChange={setIsImagePreviewOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Preview Image</DialogTitle>
          <DialogDescription>
            Preview your new profile picture before updating.
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          {selectedImage && (
            <img
              src={selectedImage}
              alt="Preview"
              className="w-64 h-64 object-cover rounded-full mx-auto"
            />
          )}
        </div>
        <div className="flex flex-col sm:flex-row justify-end gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            className="w-full sm:w-auto"
          >
            Select New Image
          </Button>
          <Button
            variant="outline"
            onClick={handleCloseImagePreview}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button onClick={handleUpdateImage} className="w-full sm:w-auto">
            Update Image
            {uploadFile.isPending && <LoadingSpinner />}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
