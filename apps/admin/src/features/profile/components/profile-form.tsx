import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import useDebounceFn from '@/hooks/use-debounce';
import React, { useRef, useState } from 'react';
import { useGetMe } from '../api/get-me';
import { useUpdateProfile } from '../api/update-profile';
import { DeleteImageModal } from './delete-image-modal';
import { ProfilePictureModal } from './profile-picture-modal';

export const ProfileForm = () => {
  const [profileName, setProfileName] = useState('');
  const { mutate: updateProfile } = useUpdateProfile();
  const debouncedUpdateProfile = useDebounceFn(updateProfile, 800);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false);
  const [isDeleteImageDialogOpen, setIsDeleteImageDialogOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const me = useGetMe();

  const handleProfileNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setProfileName(newName);
    debouncedUpdateProfile({ name: newName });
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setSelectedImage(imageUrl);
      setIsImagePreviewOpen(true);
    }
  };

  React.useEffect(() => {
    const name = me.data?.name;
    setProfileName(name ?? '');
  }, [me.data?.name]);

  return (
    <>
      <DeleteImageModal
        isOpen={isDeleteImageDialogOpen}
        setIsOpen={setIsDeleteImageDialogOpen}
      />

      <ProfilePictureModal
        isImagePreviewOpen={isImagePreviewOpen}
        setIsImagePreviewOpen={setIsImagePreviewOpen}
        fileInputRef={fileInputRef}
        selectedImage={selectedImage}
        setSelectedImage={setSelectedImage}
      />

      <div className="space-y-4 sm:space-y-6">
        <div>
          <h2 className="text-base sm:text-lg font-medium text-[#23405e] mb-1 sm:mb-2">
            Profile Settings
          </h2>
          <p className="text-xs sm:text-sm  mb-4 sm:mb-6 text-[#8D8D8D] font-nunito">
            Manage your profile information
          </p>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src="/placeholder-user.jpg" alt="Profile picture" />
            <AvatarFallback className="uppercase">
              {profileName?.[0]}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-wrap gap-2">
            <input
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              ref={fileInputRef}
              className="hidden"
            />
            <Button
              variant="outline"
              size="sm"
              className="text-[#23405e] border-[#e2e8f0] hover:bg-[#f1f5f9] hover:text-[#23405e]"
              onClick={() => fileInputRef.current?.click()}
            >
              Change Picture
            </Button>
            <Button
              variant="destructive"
              size="sm"
              className="bg-[#ff4d4f] hover:bg-[#ff7875] text-white"
              onClick={() => setIsDeleteImageDialogOpen(true)}
            >
              Delete
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <label
            htmlFor="profile-name"
            className="text-xs sm:text-sm font-medium text-[#23405e]"
          >
            Profile name
          </label>
          <Input
            id="profile-name"
            placeholder="Sara Mariyam Thomas"
            value={profileName}
            onChange={handleProfileNameChange}
            className="max-w-md border-[#e2e8f0] text-[#23405e] placeholder-[#a0aec0]"
          />
        </div>
      </div>
    </>
  );
};
