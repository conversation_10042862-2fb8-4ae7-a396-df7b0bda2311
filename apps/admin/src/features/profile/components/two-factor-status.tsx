import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { CheckCircle2, Shield } from 'lucide-react';
import { useState } from 'react';
import { useGetMe } from '../api/get-me';
import { useGetTwoFactorCode } from '../api/get-two-factor';
import { DisableTwoAuthModal } from './disable-two-auth-modal';
import { AuthenticatorModal } from './two-factor-auth-modal';

export function TwoFactorAuthCard() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [secret, setSecret] = useState('');
  const [showTwoFactorModal, setShowTwoFactorModal] = useState(false);
  const getTwoFactorCode = useGetTwoFactorCode();
  const me = useGetMe();
  const is2FAEnabled = me.data?.is_two_factor_enabled;

  const handleGetTwoFactorCode = () => {
    {
      if (!secret) {
        getTwoFactorCode.mutate(undefined, {
          onSuccess: (response) => {
            const otpauthUri = response.freeText;

            if (otpauthUri) {
              setSecret(otpauthUri);
            }

            setShowTwoFactorModal(true);
          },
        });
      } else {
        setShowTwoFactorModal(true);
      }
    }
  };

  const handleToggle = () => {
    if (is2FAEnabled) {
      setIsDialogOpen(true);
    } else {
      handleGetTwoFactorCode();
    }
  };

  return (
    <>
      <AuthenticatorModal
        secret={secret}
        open={showTwoFactorModal}
        onOpenChange={setShowTwoFactorModal}
      />

      <DisableTwoAuthModal isOpen={isDialogOpen} setIsOpen={setIsDialogOpen} />

      <Card className="w-2/5  mr-auto bg-gray-50/50">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b">
          <div className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            <CardTitle className="text-lg font-medium">
              Two-Factor Methods
            </CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Enable</span>
            <Switch checked={is2FAEnabled} onCheckedChange={handleToggle} />
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="flex items-center justify-between py-3 border-b">
            <div className="flex items-start gap-3">
              <div className="relative flex items-center justify-center">
                <div
                  className={`w-5 h-5 rounded-full ${is2FAEnabled ? 'bg-blue-100' : 'bg-gray-100'} flex items-center justify-center`}
                >
                  <CheckCircle2
                    className={`h-4 w-4 ${is2FAEnabled ? 'text-blue-600' : 'text-gray-400'}`}
                  />
                </div>
              </div>
              <div>
                <h3 className="font-medium">Authenticator App Protection</h3>
                <p className="text-sm text-muted-foreground">
                  Secure your account with time-based verification codes from
                  Google Authenticator, Authy, or similar apps
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
