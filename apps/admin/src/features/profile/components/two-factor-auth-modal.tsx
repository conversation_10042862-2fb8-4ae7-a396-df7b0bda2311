import { Dialog, DialogContent } from '@/components/ui/dialog';
import React from 'react';
import { TwoFactorAuthForm } from './two-factor-auth-form';
import { TwoFactorRecoveryForm } from './two-factor-recovery-form';

export interface AuthenticatorDialogProps {
  open: boolean;
  secret: string;
  onOpenChange: (open: boolean) => void;
}

export function AuthenticatorModal({
  open,
  onOpenChange,
  secret,
}: AuthenticatorDialogProps) {
  const [recoveryCodes, setRecoveryCodes] = React.useState<string[]>([]);

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (open === false && recoveryCodes.length > 0) {
          // reset recovery code once seen
          setRecoveryCodes([]);
        }

        onOpenChange(open);
      }}
    >
      <DialogContent className="sm:max-w-[500px] overflow-y-auto">
        {recoveryCodes.length > 0 ? (
          <TwoFactorRecoveryForm recoveryCodes={recoveryCodes} />
        ) : (
          <TwoFactorAuthForm
            onOpenChange={onOpenChange}
            secret={secret}
            setRecoveryCodes={setRecoveryCodes}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
