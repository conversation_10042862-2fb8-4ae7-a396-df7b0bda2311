import { Button } from '@/components/ui/button';
import {
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { QRCodeSVG } from 'qrcode.react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useEnableTwoFactorAuth } from '../api/enable-two-factor';
import Copy from '../assets/copy.svg';
import { AuthenticatorDialogProps } from './two-factor-auth-modal';

export const TwoFactorAuthForm = ({
  onOpenChange,
  secret,
  setRecoveryCodes,
}: Omit<AuthenticatorDialogProps, 'open'> & {
  setRecoveryCodes: React.Dispatch<React.SetStateAction<string[]>>;
}) => {
  const queryClient = useQueryClient();
  const url = new URL(secret);
  const otpAuthSecret = url.searchParams.get('secret');
  const enableTwoFactorAuth = useEnableTwoFactorAuth();
  const handleCopyKey = () => {
    navigator.clipboard.writeText(secret);
  };

  const formSchema = z.object({
    verificationCode: z
      .string()
      .length(6, { message: 'Verification code must be 6 digits' })
      .regex(/^\d+$/, { message: 'Must be digits only' }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      verificationCode: '',
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    enableTwoFactorAuth.mutate(
      {
        code: values.verificationCode,
      },
      {
        onSuccess: (response) => {
          queryClient.invalidateQueries({ queryKey: ['/auth/me'] });
          setRecoveryCodes(response);
        },
      },
    );
  };

  return (
    <div className="flex flex-col items-center space-y-6">
      <DialogHeader className="text-center">
        <DialogTitle className="text-xl">Enable authenticator app</DialogTitle>
        <DialogDescription>
          To enable 2FA, you will have to install authenticator app on your
          smart phone, either google auth or any other secure app
        </DialogDescription>
      </DialogHeader>

      <div
        style={{
          boxShadow: '0px 0px 6px 0px rgba(0, 0, 0, 0.08) inset',
          borderRadius: '12px',
          border: '1px solid #E3E3E3',
        }}
        className="w-full space-y-4 bg-[#F5F6FA] p-7"
      >
        {/* QR Code */}
        <div className="p-4 rounded-lg flex justify-center">
          {Boolean(secret) && <QRCodeSVG value={secret} />}
        </div>

        <div className="text-center">
          <h3 className="font-medium">Scan QR Code Above For The Auth App</h3>
        </div>

        <div className="space-y-2 text-center">
          <p className="text-sm text-muted-foreground">
            Having Issues Scanning QR code? Configure your app with this key
          </p>

          <div className="flex items-center justify-center gap-2 p-2 rounded">
            <code className="text-sm">{otpAuthSecret}</code>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleCopyKey}
            >
              <img src={Copy} alt="" />
            </Button>
          </div>
        </div>
      </div>

      <div className="w-full py-0">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="verificationCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Verify the code from the app</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter 6-digit code"
                      {...field}
                      maxLength={6}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                className="flex-1"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" className="flex-1">
                Continue
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};
