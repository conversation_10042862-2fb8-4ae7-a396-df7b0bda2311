import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

export type ChangePassword = {
  currentPassword: string;
  newPassword: string;
};

const changePassword = async (values: ChangePassword): Promise<string[]> => {
  const { data } = await api.put('/auth/change-password', values);
  return data;
};

export const useChangePassword = () => {
  return useMutation({
    mutationFn: changePassword,
  });
};
