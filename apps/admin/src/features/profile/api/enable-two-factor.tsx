import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

type EnableTwoFactorAuthSchema = {
  code: string;
};

const enableTwoFactorAuth = async (
  values: EnableTwoFactorAuthSchema,
): Promise<string[]> => {
  const { data } = await api.post('/auth/two-factor-auth/enable', values);
  return data;
};

export const useEnableTwoFactorAuth = () => {
  return useMutation({
    mutationFn: enableTwoFactorAuth,
  });
};
