'use client';

import LogoWithoutBorder from '@/assets/logos/logo-without-border.svg';
import { LoadingSpinner } from '@/components/loading/main';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

const projectSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
});

type ProjectFormData = z.infer<typeof projectSchema>;

interface CreateProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ProjectFormData) => void;
  isLoading?: boolean;
}

export function CreateProjectModal({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
}: CreateProjectModalProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
  });

  const handleFormSubmit = (data: ProjectFormData) => {
    onSubmit(data);
    reset();
  };

  const handleClose = () => {
    onClose();
    reset();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-[#10151c] border-[#1d2127] text-white max-w-md p-4">
        {/* Fox Icon at top center */}
        <div className="flex justify-center mb-4">
          <img width={25} height={25} src={LogoWithoutBorder} alt="" />
        </div>

        <DialogHeader>
          <DialogTitle className="text-center text-white text-xl font-semibold">
            Create New Project
          </DialogTitle>
        </DialogHeader>

        <form
          onSubmit={handleSubmit(handleFormSubmit)}
          className="space-y-4 mt-6"
        >
          <div className="space-y-2">
            <Label htmlFor="name" className="text-white">
              Project Name
            </Label>
            <Input
              id="name"
              {...register('name')}
              className="bg-[#1d2127] border-[#363b42] text-white placeholder:text-[#6b7280] focus:border-[#ff712f]"
              placeholder="Enter project name"
            />
            {errors.name && (
              <p className="text-red-400 text-sm">{errors.name.message}</p>
            )}
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={handleClose}
              className="text-[#6b7280] hover:text-white hover:bg-[#1d2127]"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-[#ff712f] hover:bg-[#ff7219] text-white"
            >
              Create Project
              {isLoading && <LoadingSpinner />}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
