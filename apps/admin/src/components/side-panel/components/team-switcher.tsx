import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronsUpDown, Plus } from 'lucide-react';
import * as React from 'react';

import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';
import { useBaseStore } from '@/store/base-store';
import { useGetWorkspace, Workspace } from '../api/get-workspace';
import { CreateModeratoDialog } from './create-moderator-dialog';

export function TeamSwitcher() {
  const { isMobile } = useSidebar();
  const { setWorkspaceId } = useBaseStore((state) => state);
  const [activeTeam, setActiveTeam] = React.useState<Workspace>(
    {} as Workspace,
  );
  const [open, setOpen] = React.useState(false); // For the Dialog
  const [dropdownOpen, setDropdownOpen] = React.useState(false); // For the DropdownMenu
  const getWorkspace = useGetWorkspace();

  React.useEffect(() => {
    if (
      getWorkspace?.data &&
      getWorkspace?.data?.length > 0 &&
      !activeTeam.id
    ) {
      const firstWorkspaace = getWorkspace.data[0];
      setActiveTeam(firstWorkspaace);
      setWorkspaceId(firstWorkspaace.id);
    }
  }, [activeTeam.id, getWorkspace.data, setWorkspaceId]);

  return (
    <>
      <CreateModeratoDialog
        open={open}
        onOpenChange={setOpen}
        setActiveTeam={setActiveTeam}
      />

      <SidebarMenu>
        <SidebarMenuItem>
          <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
            <DropdownMenuTrigger asChild onClick={() => setDropdownOpen(true)}>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  {/* <activeTeam.logo className="size-4" /> */}
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">
                    {activeTeam.name}
                  </span>
                </div>
                <ChevronsUpDown className="ml-auto" />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
              align="start"
              side={isMobile ? 'bottom' : 'right'}
              sideOffset={4}
            >
              <DropdownMenuLabel className="text-xs text-muted-foreground">
                Workspaces
              </DropdownMenuLabel>
              {getWorkspace.data?.map((team, index) => (
                <DropdownMenuItem
                  key={team.name}
                  onClick={() => {
                    setActiveTeam(team);
                    setWorkspaceId(team.id);
                  }}
                  className="gap-2 p-2"
                >
                  {/* <div className="flex size-6 items-center justify-center rounded-sm border">
                    <team.logo className="size-4 shrink-0" />
                  </div> */}
                  {team.name}
                  <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem className="gap-2 p-2">
                <div className="flex size-6 items-center justify-center rounded-md border bg-background">
                  <Plus className="size-4" />
                </div>
                <div
                  onClick={() => {
                    setOpen(true); // Open the dialog
                    setDropdownOpen(false); // Close the dropdown
                  }}
                  className="font-medium text-muted-foreground"
                >
                  Add Workspace
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
    </>
  );
}
