import LogoSmall from '@/assets/logos/logo-small.svg';
import { LoadingSpinner } from '@/components/loading/main';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { errorParser } from '@/lib/errors';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { useBaseStore } from '@/store/base-store';
import { zodResolver } from '@hookform/resolvers/zod';
import { DialogDescription } from '@radix-ui/react-dialog';
import { useQueryClient } from '@tanstack/react-query';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useCreateWorkspace } from '../api/create-workspace';
import { Workspace } from '../api/get-workspace';
import Ai from '../assets/ai.png';

const AI_AVATARS = [Ai, Ai, Ai, Ai];

const createAiSchema = z.object({
  name: z
    .string()
    .min(2, {
      message: 'Name must be at least 2 characters.',
    })
    .nonempty({
      message: 'Name is required.',
    }),
});

interface AiModeratorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  setActiveTeam: React.Dispatch<React.SetStateAction<Workspace>>;
}

export function CreateModeratoDialog({
  open,
  onOpenChange,
  setActiveTeam,
}: AiModeratorDialogProps) {
  const [selectedAvatar, setSelectedAvatar] = React.useState(0);
  const createWorkspace = useCreateWorkspace();
  const queryClient = useQueryClient();
  const { setWorkspaceId } = useBaseStore((state) => state);

  const form = useForm<z.infer<typeof createAiSchema>>({
    resolver: zodResolver(createAiSchema),
    defaultValues: {
      name: '',
    },
  });

  const onSubmit = (values: z.infer<typeof createAiSchema>) => {
    createWorkspace.mutate(
      {
        name: values.name,
      },
      {
        onSuccess: (response) => {
          queryClient.invalidateQueries({ queryKey: ['/workspace'] });
          setActiveTeam(response);
          setWorkspaceId(response?.id);
          toast.success('Workspace created successfully');
          onOpenChange(false);
        },
        onError: (error) => {
          toast.error(errorParser(error));
        },
      },
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[32rem]">
        <DialogDescription className="sr-only">
          Create a new AI Moderator
        </DialogDescription>
        <DialogHeader>
          <div className="flex justify-center items-center text-sm text-muted-foreground mb-4">
            <img
              src={LogoSmall || '/placeholder.svg'}
              alt="Logo"
              width={32}
              height={32}
              className="mx-auto"
            />
          </div>
          <DialogTitle className="text-2xl font-medium text-center mb-8 font-nunito">
            What would you like to call your
            <br />
            AI Moderator?
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <div className="relative mx-auto w-28 h-28">
                <img
                  src={AI_AVATARS[selectedAvatar] || '/placeholder.svg'}
                  alt="Selected Avatar"
                  width={112}
                  height={112}
                  className="w-full h-full rounded-full border-4 border-transparent transition-transform duration-200 hover:scale-105"
                />
              </div>
              <RadioGroup
                value={selectedAvatar.toString()}
                onValueChange={(value) => setSelectedAvatar(parseInt(value))}
                className="flex justify-center gap-4"
              >
                {AI_AVATARS.map((avatar, i) => (
                  <div key={i} className="relative">
                    <RadioGroupItem
                      value={i.toString()}
                      id={`avatar-${i}`}
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor={`avatar-${i}`}
                      className={cn(
                        'block w-12 h-12 rounded-full cursor-pointer border-2 transition-all duration-200',
                        'hover:scale-105',
                        selectedAvatar === i
                          ? 'border-black shadow-lg'
                          : 'border-transparent opacity-70 hover:opacity-100',
                      )}
                    >
                      <img
                        src={avatar || '/placeholder.svg'}
                        alt={`Avatar option ${i + 1}`}
                        width={48}
                        height={48}
                        className="w-full h-full rounded-full"
                      />
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="moderator-name"
                        placeholder="Enter Name Here"
                        className={cn(
                          'text-left text-base',
                          form.formState.errors.name &&
                            'border-destructive focus-visible:ring-destructive',
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex justify-between gap-4">
              <Button
                type="button"
                variant="outline"
                className="flex-1"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" className="flex-1">
                Submit
                {createWorkspace.isPending && <LoadingSpinner />}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
