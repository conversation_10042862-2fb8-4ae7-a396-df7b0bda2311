import { api } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export interface Workspace {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
  deleted_at: null;
  owner_id: number;
  members: unknown[];
  owner: Owner;
}

interface Owner {
  id: number;
  email: string;
  password_hash: string;
  last_login: string;
  created_at: string;
  updated_at: string;
  deleted_at: null;
  is_active: boolean;
  is_email_verified: boolean;
  email_verified_at: string;
  verification_secret: string;
  is_two_factor_enabled: boolean;
  two_factor_secret: string;
}

const getWorkspace = async (): Promise<Workspace[]> => {
  return api.get('/workspace');
};

export const useGetWorkspace = () => {
  return useQuery({
    queryKey: ['/workspace'],
    queryFn: getWorkspace,
  });
};
