import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { Workspace } from './get-workspace';

export type CreateWorkspacePayload = {
  name: string;
};

const createWorkspace = async (
  payload: CreateWorkspacePayload,
): Promise<Workspace> => {
  return api.post('/workspace', payload);
};

export const useCreateWorkspace = () => {
  return useMutation({
    mutationFn: createWorkspace,
  });
};
