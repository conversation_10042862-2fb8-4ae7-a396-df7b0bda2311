import { useState } from 'react';

import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { CheckListPlugin } from '@lexical/react/LexicalCheckListPlugin';
import { ClickableLinkPlugin } from '@lexical/react/LexicalClickableLinkPlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { HorizontalRulePlugin } from '@lexical/react/LexicalHorizontalRulePlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { TabIndentationPlugin } from '@lexical/react/LexicalTabIndentationPlugin';

import { Separator } from '@/components/ui/separator';

import { BlockFormatDropDown } from '@/components/editor/plugins/toolbar/block-format-toolbar-plugin';
import { FormatBulletedList } from '@/components/editor/plugins/toolbar/block-format/format-bulleted-list';
import { FormatHeading } from '@/components/editor/plugins/toolbar/block-format/format-heading';
import { FormatParagraph } from '@/components/editor/plugins/toolbar/block-format/format-paragraph';
// import { FormatNumberedList } from '@/components/editor/plugins/toolbar/block-format/format-numbered-list';
// import { FormatQuote } from '@/components/editor/plugins/toolbar/block-format/format-quote';
// import { FormatCheckList } from '@/components/editor/plugins/toolbar/block-format/format-check-list';
import { FontFormatToolbarPlugin } from '@/components/editor/plugins/toolbar/font-format-toolbar-plugin';
import { HistoryToolbarPlugin } from '@/components/editor/plugins/toolbar/history-toolbar-plugin';
import { ToolbarPlugin } from '@/components/editor/plugins/toolbar/toolbar-plugin';

import { ContentEditable } from '@/components/editor/editor-ui/content-editable';
import { AutocompletePlugin } from '@/components/editor/plugins/autocomplete-plugin';
import { DraggableBlockPlugin } from '@/components/editor/plugins/draggable-block-plugin';
import { LayoutPlugin } from '@/components/editor/plugins/layout-plugin';
import { ListMaxIndentLevelPlugin } from '@/components/editor/plugins/list-max-indent-level-plugin';
import { TabFocusPlugin } from '@/components/editor/plugins/tab-focus-plugin';

export const placeholder = 'Press / for commands...';

export function Plugins() {
  const [floatingAnchorElem, setFloatingAnchorElem] =
    useState<HTMLDivElement | null>(null);

  const onRef = (_floatingAnchorElem: HTMLDivElement) => {
    if (_floatingAnchorElem !== null) {
      setFloatingAnchorElem(_floatingAnchorElem);
    }
  };

  return (
    <div className="relative">
      <ToolbarPlugin>
        {({ blockType }) => (
          <div className="vertical-align-middle sticky top-0 z-10 flex gap-2 overflow-auto border-b p-1">
            <HistoryToolbarPlugin />
            <Separator orientation="vertical" className="h-8" />
            <BlockFormatDropDown>
              <FormatParagraph />
              <FormatHeading levels={['h3']} />
              {/* <FormatNumberedList /> */}
              <FormatBulletedList />
              {/* <FormatCheckList /> */}
              {/* <FormatCodeBlock /> */}
              {/* <FormatQuote /> */}
            </BlockFormatDropDown>
            {blockType === 'code' ? null : (
              <>
                <Separator orientation="vertical" className="h-8" />
                <FontFormatToolbarPlugin format="bold" />
                <FontFormatToolbarPlugin format="italic" />
                <Separator orientation="vertical" className="h-8" />
              </>
            )}
          </div>
        )}
      </ToolbarPlugin>
      <div className="relative flex-1 flex flex-col min-h-0">
        <AutoFocusPlugin />
        <RichTextPlugin
          contentEditable={
            <div className="flex-1 flex flex-col min-h-0">
              <div className="flex-1 flex flex-col min-h-0" ref={onRef}>
                <ContentEditable
                  placeholder={placeholder}
                  className="ContentEditable__root relative block h-[385px] overflow-y-auto px-8 py-4 focus:outline-none"
                />
              </div>
            </div>
          }
          ErrorBoundary={LexicalErrorBoundary}
        />

        <ClickableLinkPlugin />
        <CheckListPlugin />
        <HorizontalRulePlugin />
        <ListPlugin />
        <TabIndentationPlugin />
        <HistoryPlugin />

        <DraggableBlockPlugin anchorElem={floatingAnchorElem} />
        <LayoutPlugin />

        <TabFocusPlugin />
        <AutocompletePlugin />
        <ListMaxIndentLevelPlugin />
      </div>
    </div>
  );
}
