import Logo from '@/assets/logos/logo.svg';
import { cookie } from '@/lib/cookie';
import { motion } from 'motion/react';
import { useEffect } from 'react';
import { Outlet, useNavigate } from 'react-router';
import BgBottomVector from './assets/bg-bottom-vector.svg';
import BgTopVector from './assets/bg-top-vector.svg';
import './styles/index.css';

export default function AuthLayout() {
  const navigate = useNavigate();
  const accessToken = cookie.get('access_token');
  const condition = false;

  // TODO: for 2factor verify there since there is token it is being redirected
  // to dashboard. Fix when time
  useEffect(() => {
    if (condition && accessToken) {
      navigate('/');
    }
  }, [accessToken, navigate, condition]);

  return (
    // <div className="min-h-screen relative flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 px-4 py-8">
    <div className="min-h-screen relative flex flex-col items-center justify-center bg-gradient-to-br from-[#0D1117] to-[#000000] px-4 py-8">
      {/* Logo */}
      <div className="mb-8 z-10 relative media-h-sm-plus:fixed  top-[6%] mx-auto">
        <img className="scale-75 md:scale-100" src={Logo} alt="" />
      </div>

      {/* Content */}
      <div className="flex flex-col fixed top-[30%] self-center items-center justify-center">
        {/* Top Vector */}
        <div className="relative z-10">
          <img src={BgTopVector} alt="" />

          <motion.div
            className="top-vector-glow vector-glow"
            initial={{ offsetDistance: '0%', scale: 1 }}
            animate={{ offsetDistance: '100%', scale: 1 }}
            transition={{
              duration: 8,
              repeatType: 'loop',
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          ></motion.div>
        </div>

        {/* Center moving vector */}
        <motion.div
          className="h-[90vh] w-[250px] absolute  z-0 blur-[85px]"
          style={{
            background:
              'linear-gradient(181.2deg, rgba(83, 129, 248, 0.13) -7.25%, rgba(134, 198, 245, 0.13) 90.14%)',
          }}
          initial={{ rotate: -55, y: -30 }}
          animate={{ rotate: 55, y: -30 }}
          transition={{
            duration: 8,
            repeatType: 'reverse',
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        ></motion.div>

        {/* Bottom Vector */}
        <div className="z-10 relative md:bottom-[30px] lg:bottom-[75px]">
          <img src={BgBottomVector} alt="" />

          <motion.div
            className="bottom-vector-glow vector-glow"
            initial={{ offsetDistance: '0%', scale: 1 }}
            animate={{ offsetDistance: '100%', scale: 1 }}
            transition={{
              duration: 8,
              repeatType: 'loop',
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          ></motion.div>
        </div>
      </div>

      {/* Children  */}
      <Outlet />

      {/* Footer */}
      <footer className="text-center text-xs md:text-sm text-muted-foreground z-10 bottom-[2%] md:bottom-[5%] mx-auto relative mt-6 media-h-sm-plus:fixed media-h-sm-plus:mt-0">
        <div className="flex flex-col sm:flex-row gap-2 md:gap-4 justify-center">
          <a href="#" className="hover:underline">
            © Fullstackfox
          </a>
          <a href="#" className="hover:underline">
            Support
          </a>
          <a href="#" className="hover:underline">
            Terms and Condition
          </a>
        </div>
      </footer>
    </div>
  );
}
