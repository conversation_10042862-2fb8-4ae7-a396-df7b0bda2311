<svg width="754" height="101" viewBox="0 0 754 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_17_382)">
<path d="M4 92H120.5L208.5 1H529L599.5 92H749.5" stroke="url(#paint0_radial_17_382)" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_17_382" x="0" y="0.5" width="753.5" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.68419 0 0 0 0 0.789171 0 0 0 0 0.992959 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_17_382"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_17_382" result="shape"/>
</filter>
<radialGradient id="paint0_radial_17_382" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(367.731 11.9972) rotate(-0.167848) scale(329.294 170.242)">
<stop stop-color="#0879E1"/>
<stop offset="0.535878" stop-color="#C88DF6"/>
<stop offset="1" stop-color="#0879E1" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
