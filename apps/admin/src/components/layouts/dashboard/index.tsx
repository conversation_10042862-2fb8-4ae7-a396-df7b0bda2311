import { AppSidebar } from '@/components/side-panel/main';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { cookie } from '@/lib/cookie';
import { toast } from '@/lib/toast';
import { useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router';

export default function DashboardLayout() {
  const navigate = useNavigate();
  const accessToken = cookie.get('access_token');
  const pathname = useLocation().pathname;
  const isChatPage = pathname.includes('chat');
  const isOnboardingPage = pathname.includes('onboarding');

  useEffect(() => {
    if (!accessToken) {
      toast.error('Please login first');
      navigate('/auth/login');
    }
  }, [accessToken, navigate]);

  return (
    <>
      {!isChatPage && !isOnboardingPage ? (
        <SidebarProvider>
          <AppSidebar />

          <SidebarInset className="bg-[#0D1117]">
            {/* <div className="flex flex-col h-full">
              <header className="">
                <div className="flex flex-col sm:flex-row h-auto sm:h-16 items-start sm:items-center px-4 py-2 sm:py-0 justify-between">
                  <header className="">
                    <div className="flex items-center justify-between max-w-7xl mx-auto">
                      <div className="flex items-center space-x-2 text-sm text-[#406D9A]">
                        <SidebarTrigger />

                        <a href="/dashboard" className="hover:text-[#23405e]">
                          Dashboard
                        </a>
                        <ChevronRight className="h-4 w-4" />
                        <span className="text-[#23405e] font-medium">
                          Settings
                        </span>
                      </div>
                    </div>
                  </header>
                </div>
              </header>

              <Outlet />
            </div> */}
            <Outlet />
          </SidebarInset>
        </SidebarProvider>
      ) : (
        <Outlet />
      )}
    </>
  );
}
