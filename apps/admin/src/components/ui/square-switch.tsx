'use client';

import * as SwitchPrimitives from '@radix-ui/react-switch';
import * as React from 'react';
import { useEffect, useState } from 'react';

import { cn } from '@/lib/utils';
import { AppWindow, Code } from 'lucide-react';

const SquareSwitch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, checked, defaultChecked, ...props }, ref) => {
  const [isChecked, setIsChecked] = useState(
    checked || defaultChecked || false,
  );

  useEffect(() => {
    if (checked !== undefined) {
      setIsChecked(checked);
    }
  }, [checked]);

  return (
    <SwitchPrimitives.Root
      className={cn(
        'peer inline-flex h-8 w-14 shrink-0 cursor-pointer items-center rounded-lg border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 ',
        // 'data-[state=checked]:bg-primary data-[state=unchecked]:bg-input',
        'bg-primary',
        className,
      )}
      checked={checked}
      defaultChecked={defaultChecked}
      onCheckedChange={(value) => {
        setIsChecked(value);
        if (props.onCheckedChange) {
          props.onCheckedChange(value);
        }
      }}
      {...props}
      ref={ref}
    >
      <SwitchPrimitives.Thumb
        className={cn(
          'pointer-events-none flex items-center justify-center h-7 w-7 rounded-lg bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-6 data-[state=unchecked]:translate-x-0',
        )}
      >
        {isChecked ? (
          <AppWindow className="w-4 h-4" />
        ) : (
          <Code className="w-4 h-4" />
        )}
      </SwitchPrimitives.Thumb>
    </SwitchPrimitives.Root>
  );
});
SquareSwitch.displayName = SwitchPrimitives.Root.displayName;

export { SquareSwitch };
