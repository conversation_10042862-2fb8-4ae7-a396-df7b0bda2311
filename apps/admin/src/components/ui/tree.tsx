'use client';

import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import * as AccordionPrimitive from '@radix-ui/react-accordion';
import { ChevronRight, type LucideIcon } from 'lucide-react';
import React from 'react';
import useResizeObserver from 'use-resize-observer';

interface TreeDataItem {
  id: string;
  name: string;
  icon?: LucideIcon;
  children?: TreeDataItem[];
  path?: string;
}

type TreeProps = React.HTMLAttributes<HTMLDivElement> & {
  data: TreeDataItem[] | TreeDataItem;
  initialSlelectedItemId?: string;
  onSelectChange?: (item: TreeDataItem | undefined) => void;
  expandAll?: boolean;
  folderIcon?: LucideIcon;
  itemIcon?: LucideIcon;
};

const Tree = React.forwardRef<HTMLDivElement, TreeProps>(
  (
    {
      data,
      initialSlelectedItemId,
      onSelectChange,
      expandAll,
      folderIcon,
      itemIcon,
      className,
      ...props
    },
    ref,
  ) => {
    const [selectedItemId, setSelectedItemId] = React.useState<
      string | undefined
    >(initialSlelectedItemId);

    // State to track which folders are expanded
    const [expandedItemIds, setExpandedItemIds] = React.useState<string[]>(
      () => {
        if (!initialSlelectedItemId || expandAll) {
          return expandAll ? getAllFolderIds(data) : [];
        }

        // Only expand the path to the initially selected item
        const ids: string[] = [];
        function walkTreeItems(
          items: TreeDataItem[] | TreeDataItem,
          targetId: string,
          currentPath: string[] = [],
        ): boolean {
          if (items instanceof Array) {
            for (let i = 0; i < items.length; i++) {
              const item = items[i]!;
              const newPath = [...currentPath, item.id];

              if (item.id === targetId) {
                ids.push(...currentPath);
                return true;
              }

              if (
                item.children &&
                walkTreeItems(item.children, targetId, newPath)
              ) {
                ids.push(...currentPath);
                return true;
              }
            }
          } else if (items.id === targetId) {
            ids.push(...currentPath);
            return true;
          } else if (items.children) {
            return walkTreeItems(items.children, targetId, currentPath);
          }
          return false;
        }

        walkTreeItems(data, initialSlelectedItemId);
        return [...new Set(ids)]; // Remove duplicates
      },
    );

    const handleSelectChange = React.useCallback(
      (item: TreeDataItem | undefined) => {
        setSelectedItemId(item?.id);
        if (onSelectChange) {
          onSelectChange(item);
        }
      },
      [onSelectChange],
    );

    // Helper function to get all folder IDs for expandAll
    function getAllFolderIds(items: TreeDataItem[] | TreeDataItem): string[] {
      const ids: string[] = [];

      function traverse(nodes: TreeDataItem[] | TreeDataItem) {
        if (nodes instanceof Array) {
          nodes.forEach((node) => {
            if (node.children) {
              ids.push(node.id);
              traverse(node.children);
            }
          });
        } else if (nodes.children) {
          ids.push(nodes.id);
          traverse(nodes.children);
        }
      }

      traverse(items);
      return ids;
    }

    const { ref: refRoot, width, height } = useResizeObserver();

    return (
      <div ref={refRoot} className={cn('overflow-hidden', className)}>
        <ScrollArea style={{ width, height }}>
          <div className="relative p-2">
            <TreeItem
              data={data}
              ref={ref}
              selectedItemId={selectedItemId}
              handleSelectChange={handleSelectChange}
              expandedItemIds={expandedItemIds}
              setExpandedItemIds={setExpandedItemIds}
              FolderIcon={folderIcon}
              ItemIcon={itemIcon}
              {...props}
            />
          </div>
        </ScrollArea>
      </div>
    );
  },
);

type TreeItemProps = TreeProps & {
  selectedItemId?: string;
  handleSelectChange: (item: TreeDataItem | undefined) => void;
  expandedItemIds: string[];
  setExpandedItemIds: React.Dispatch<React.SetStateAction<string[]>>;
  FolderIcon?: LucideIcon;
  ItemIcon?: LucideIcon;
};

const TreeItem = React.forwardRef<HTMLDivElement, TreeItemProps>(
  (
    {
      className,
      data,
      selectedItemId,
      handleSelectChange,
      expandedItemIds,
      setExpandedItemIds,
      FolderIcon,
      ItemIcon,
      ...props
    },
    ref,
  ) => {
    return (
      <div ref={ref} role="tree" className={className} {...props}>
        <ul>
          {data instanceof Array ? (
            data.map((item) => (
              <li key={item.id}>
                {item.children ? (
                  <AccordionPrimitive.Root
                    type="multiple"
                    value={expandedItemIds}
                    onValueChange={setExpandedItemIds}
                  >
                    <AccordionPrimitive.Item value={item.id}>
                      <AccordionTrigger
                        // className={cn(
                        //   'px-2 [&>svg]:-order-1 [&>svg]:text-[#F4F4F5] justify-start gap-3 hover:before:opacity-100 before:absolute before:left-0 before:w-full before:opacity-0 before:bg-muted/80 before:h-[1.75rem] before:-z-10 ',
                        //   selectedItemId === item.id &&
                        //     'before:opacity-100 before:bg-accent text-accent-foreground before:border-l-2 before:border-l-accent-foreground/50 dark:before:border-0',
                        // )}
                        className={cn(
                          'justify-start pl-2 text-left gap-3 py-2 text-[15px] leading-6 hover:no-underline [&>svg]:-order-1 [&>svg]:ml-0 [&[data-state=open]>svg]:rotate-90 [&>svg]:text-white-100',
                          selectedItemId === item.id && 'bg-black-100',
                        )}
                        onClick={() => handleSelectChange(item)}
                      >
                        {/* {ItemIcon && (
                          <ItemIcon
                            className="h-4 w-4 shrink-0 mr-2 text-[#F4F4F5]"
                            aria-hidden="true"
                          />
                        )} */}
                        {/* {!item.icon && FolderIcon && (
                          <FolderIcon
                            className="h-4 w-4 shrink-0 mr-2 text-accent-foreground/50"
                            aria-hidden="true"
                          />
                        )} */}
                        <span className="text-sm truncate mr-auto">
                          {item.name}
                        </span>
                      </AccordionTrigger>
                      <AccordionContent className="pl-6">
                        <TreeItem
                          data={item.children ? item.children : item}
                          selectedItemId={selectedItemId}
                          handleSelectChange={handleSelectChange}
                          expandedItemIds={expandedItemIds}
                          setExpandedItemIds={setExpandedItemIds}
                          FolderIcon={FolderIcon}
                          ItemIcon={ItemIcon}
                        />
                      </AccordionContent>
                    </AccordionPrimitive.Item>
                  </AccordionPrimitive.Root>
                ) : (
                  <Leaf
                    item={item}
                    isSelected={selectedItemId === item.id}
                    onClick={() => handleSelectChange(item)}
                    Icon={ItemIcon}
                  />
                )}
              </li>
            ))
          ) : (
            <li>
              <Leaf
                item={data}
                isSelected={selectedItemId === data.id}
                onClick={() => handleSelectChange(data)}
                Icon={ItemIcon}
              />
            </li>
          )}
        </ul>
      </div>
    );
  },
);

const Leaf = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    item: TreeDataItem;
    isSelected?: boolean;
    Icon?: LucideIcon;
  }
>(({ className, item, isSelected, Icon, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        'flex items-center py-2 px-2 cursor-pointer \
        hover:before:opacity-100 before:absolute before:left-0 before:right-1 before:w-full before:opacity-0 before:bg-muted/80 before:h-[1.75rem] before:-z-10',
        className,
        isSelected &&
          'before:opacity-100 bg-black-100 before:bg-accent  before:border-l-2 before:border-l-accent-foreground/50 dark:before:border-0',
      )}
      {...props}
    >
      {/* {item.icon && (
        <item.icon
          className="h-4 w-4 shrink-0 mr-2 text-accent-foreground/50"
          aria-hidden="true"
        />
      )} */}
      {Icon && (
        <Icon
          className="h-4 w-4 shrink-0 mr-2 text-white-100"
          aria-hidden="true"
        />
      )}
      <span className="flex-grow text-sm truncate">{item.name}</span>
    </div>
  );
});

const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Header>
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        'flex flex-1 w-full items-center py-2 transition-all last:[&[data-state=open]>svg]:rotate-90',
        className,
      )}
      {...props}
    >
      <ChevronRight className="h-4 w-4 shrink-0 transition-transform duration-200 text-accent-foreground/50 ml-auto" />
      {children}
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
));
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    className={cn(
      'overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down',
      className,
    )}
    {...props}
  >
    <div className="pb-1 pt-0">{children}</div>
  </AccordionPrimitive.Content>
));
AccordionContent.displayName = AccordionPrimitive.Content.displayName;

export { Tree, type TreeDataItem };
