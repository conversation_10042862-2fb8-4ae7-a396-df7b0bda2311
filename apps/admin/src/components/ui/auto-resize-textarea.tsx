'use client';

import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import * as React from 'react';

interface AutoResizeTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  maxHeight?: number;
}

export function AutoResizeTextarea({
  maxHeight = 200,
  className,
  onChange,
  ...props
}: AutoResizeTextareaProps) {
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';

    // Set the height based on scrollHeight, but cap it at maxHeight
    const newHeight = Math.min(textarea.scrollHeight, maxHeight);
    textarea.style.height = `${newHeight}px`;

    // Call the original onChange handler if provided
    onChange?.(event);
  };

  // Initialize height on mount and when content changes
  React.useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Set initial height
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, maxHeight)}px`;
  }, [maxHeight, props.value, props.defaultValue]);

  return (
    <Textarea
      ref={textareaRef}
      onChange={handleChange}
      className={cn(
        'transition-height duration-200 ease-out overflow-hidden',
        maxHeight ? `max-h-[${maxHeight}px]` : '',
        className,
      )}
      style={{
        overflowY:
          textareaRef.current?.scrollHeight &&
          textareaRef.current?.scrollHeight > maxHeight
            ? 'auto'
            : 'hidden',
      }}
      {...props}
    />
  );
}
