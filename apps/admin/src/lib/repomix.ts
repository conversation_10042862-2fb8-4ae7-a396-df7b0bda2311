import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

// Define the types for better type safety
export interface FileObject {
  [filePath: string]: string;
}

export interface RepomixResult {
  content: string;
  totalFiles: number;
  totalCharacters: number;
  totalTokens: number;
  fileCharCounts: Record<string, number>;
  fileTokenCounts: Record<string, number>;
  fileTree: string;
  fileStats: {
    totalFiles: number;
    filesByExtension: { [ext: string]: number };
  };
}

export interface RepomixConfig {
  output?: {
    showLineNumbers?: boolean;
    removeComments?: boolean;
    removeEmptyLines?: boolean;
    style?: 'markdown' | 'xml' | 'plain';
    compress?: boolean;
    fileSummary?: boolean;
    directoryStructure?: boolean;
  };
}

export interface RepomixRequest {
  files: FileObject;
  config?: RepomixConfig;
}

// API functions
const generateRepoPack = async (
  request: RepomixRequest,
): Promise<RepomixResult> => {
  return api.post('/api/repomix/generate', request);
};

// TanStack Query hooks
export const useGenerateRepoPack = () => {
  return useMutation({
    mutationFn: generateRepoPack,
  });
};
