import { Toaster } from '@/components/ui/sonner';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Suspense } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { BrowserRouter, useRoutes } from 'react-router';
import { MainErrorFallback } from './components/errors/main.tsx';
import { routes } from './config/routes.tsx';

const queryClient = new QueryClient();

function AppRoutes() {
  return useRoutes(routes);
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary FallbackComponent={MainErrorFallback}>
        <BrowserRouter>
          <Suspense fallback={null}>
            <AppRoutes />
          </Suspense>
          <Toaster richColors theme="light" />
        </BrowserRouter>
      </ErrorBoundary>
    </QueryClientProvider>
  );
}

export default App;
