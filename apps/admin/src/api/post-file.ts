import { api } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

export type Create = {
  file: FormData;
};

export type FileUploadResponse = {
  id: string;
  name: string;
};

const uploadFile = async (payload: Create): Promise<FileUploadResponse> => {
  const { data } = await api.post<FileUploadResponse>('/files/upload', payload);
  return data;
};

export const useUploadFile = () => {
  return useMutation({
    mutationFn: uploadFile,
  });
};
