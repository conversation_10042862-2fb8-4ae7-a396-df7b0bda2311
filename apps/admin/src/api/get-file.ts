import { api } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

interface GetMeResponse {
  id: number;
  email: string;
  is_email_verified: boolean;
  is_active: boolean;
  is_two_factor_enabled: boolean;
  name: string;
}

const getFile = async (): Promise<GetMeResponse> => {
  const { data } = await api.get('/files');

  return data;
};

export const useGetFile = ({ key }: { key: string }) => {
  return useQuery({
    queryKey: ['/auth/me', key],
    queryFn: getFile,
  });
};
