version: '3.8'

services:
  core:
    build:
      context: .
      dockerfile: Dockerfile.core
    restart: always
    ports:
      - '9700:9700'
    environment:
      - NODE_ENV=production
      - COREAPI_PORT=9700
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - JWT_ACCESS_SECRET=${JWT_ACCESS_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - JWT_2FA_ACCESS_SECRET=${JWT_2FA_ACCESS_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_REDIRECT_URI=${GOOGLE_REDIRECT_URI}
      - OAUTH_GOOGLE_STATE=${OAUTH_GOOGLE_STATE}
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      - BASE_REPO_OWNER=${BASE_REPO_OWNER}
      - BASE_REPO_NAME=${BASE_REPO_NAME}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_GENERATIVE_AI_API_KEY=${GOOGLE_GENERATIVE_AI_API_KEY}
      - SUPA_CONNECT_CLIENT_ID=${SUPA_CONNECT_CLIENT_ID}
      - SUPA_CONNECT_CLIENT_SECRET=${SUPA_CONNECT_CLIENT_SECRET}
      - SUPA_CONNECT_REDIRECT_URI=${SUPA_CONNECT_REDIRECT_URI}
      - VERCEL_CLIENT_ID=${VERCEL_CLIENT_ID}
      - VERCEL_CLIENT_SECRET=${VERCEL_CLIENT_SECRET}
      - VERCEL_REDIRECT_URI=${VERCEL_REDIRECT_URI}
    volumes:
      - core_data:/app/data
    depends_on:
      - worker

  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    restart: always
    ports:
      - '8001:8001'
      - '8002:8002'
    environment:
      - NODE_ENV=production
      - SERVER_PORT=${SERVER_PORT}
      - WORKER_PORT=${WORKER_PORT}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - BUILD_TIMEOUT=${BUILD_TIMEOUT}
      - MAX_MEMORY=${MAX_MEMORY}
      - MAX_CPU=${MAX_CPU}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - worker_data:/app/data
      - worker_outputs:/app/outputs
      - worker_uploads:/app/uploads
      - worker_shared:/app/shared

  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - '80:80'
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - core
      - worker

volumes:
  core_data:
  worker_data:
  worker_outputs:
  worker_uploads:
  worker_shared:
