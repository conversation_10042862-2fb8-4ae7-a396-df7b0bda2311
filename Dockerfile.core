FROM node:18-alpine AS builder

# Install pnpm and OpenSSL
RUN apk add --no-cache openssl
RUN npm install -g pnpm@9.15.0

WORKDIR /app
COPY . .

# Install dependencies
RUN pnpm install

# Build the core app
RUN pnpm turbo run build --filter=coreapi

# Production image
FROM node:18-alpine

# Install OpenSSL
RUN apk add --no-cache openssl

WORKDIR /app

# Copy built assets from builder stage
COPY --from=builder /app/apps/core/dist ./dist
COPY --from=builder /app/apps/core/package.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/packages ./packages

# Set environment variables
ENV NODE_ENV=production

# Start the application
CMD ["node", "dist/src/main.js"]