FROM node:18-alpine AS builder

# Install pnpm and OpenSSL
RUN apk add --no-cache openssl
RUN npm install -g pnpm@9.15.0

WORKDIR /app
COPY . .

# Install dependencies
RUN pnpm install

# Build the worker app
RUN pnpm turbo run build --filter=@iammoderator/worker

# Production image
FROM node:18-alpine

# Install Docker CLI and OpenSSL
RUN apk add --no-cache docker openssl

WORKDIR /app

# Create necessary directories
RUN mkdir -p data outputs uploads shared

# Copy built assets from builder stage
COPY --from=builder /app/apps/worker/dist ./dist
COPY --from=builder /app/apps/worker/package.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/packages ./packages

# Set environment variables
ENV NODE_ENV=production

# Start the application
CMD ["node", "dist/server.js"]