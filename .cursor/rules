# General Code Style
Use camelCase for variables and functions.
Use PascalCase for components and classes.
Use 2 spaces for indentation.
Always use semicolons.
Prefer const over let, and let over var.
Use single quotes for strings.
Use trailing commas in objects, arrays, and parameters when multiline.

# React / Frontend Rules
Use functional components with hooks.
Use arrow functions for component declarations.
Use TypeScript for all new files.
Avoid prop drilling; use context or props when appropriate.
Write components that are reusable and modular.
Use Tailwind CSS for styling if applicable.

# File Structure
Group related components, hooks, and utilities into folders.
Each component should have its own file.

# Documentation and Comments
Write concise and helpful inline comments where logic is non-obvious.
Avoid redundant comments.

# API and State
Use SWR or React Query for data fetching.
Prefer async/await over .then/.catch.
Use Zustand or Redux Toolkit for global state when needed.
Avoid unnecessary API calls in components — isolate them in services.

# Testing
Write tests for every new component or utility.
Use Jest and React Testing Library.
Test edge cases and failure states, not just happy paths.

# Git & Workflow
Write clear commit messages: <type>(scope): description
Types: feat, fix, refactor, test, chore, docs
Keep pull requests small and focused.
Use feature branches and avoid committing directly to main.
Make sure that the commit message does not exceed 100 characters.

# AI Use
When asked to generate code, prefer correctness and readability over cleverness.
Do not introduce external libraries unless explicitly instructed.
Follow project structure and existing patterns.
